name: 🛠 Android CI / Build & Release

on:
  push:
    branches: [ main ]
    tags:    [ 'v*.*.*' ]
  pull_request:
    branches: [ main ]

permissions:
  contents: write   # allow creating/updating Releases & uploading assets
  actions:  read    # allow pulling marketplace actions

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_and_release:
    runs-on: ubuntu-latest

    steps:
      - name: 📂 Checkout code
        uses: actions/checkout@v3

      - name: 🚀 Cache pub deps
        uses: actions/cache@v3
        with:
          path: ~/.pub-cache
          key: ${{ runner.os }}-pub-${{ hashFiles('**/pubspec.yaml') }}
          restore-keys: ${{ runner.os }}-pub-

      - name: ☕️ Setup Java (Temurin 17)
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: '17'

      - name: 🦋 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable

      - name: 📥 Get dependencies
        run: flutter pub get

      - name: ✅ Run tests
        run: flutter test --coverage

      # 构建 APK (多架构)
      - name: 🏗️ Build APKs
        run: flutter build apk --release --split-per-abi

      # 构建 Google Play 用的 AAB
      - name: 📦 Build AAB (Google Play)
        run: flutter build appbundle --release

      - name: 📁 Prepare artifacts
        run: |
          mkdir -p artifacts
          # 复制 APK 文件
          cp build/app/outputs/flutter-apk/*.apk artifacts/
          # 复制 AAB 文件
          cp build/app/outputs/bundle/release/*.aab artifacts/
          # 添加构建信息文件
          echo "Commit: $GITHUB_SHA" > artifacts/build-info.txt
          echo "Build Date: $(date)" >> artifacts/build-info.txt

      # ─────────────── 每次 PUSH 到 main 都发布 ───────────────
      - name: 🚀 Create Release on Main Push
        if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}
        uses: ncipollo/release-action@v1.16.0
        with:
          # 使用 commit SHA 前7位作为版本标识
          tag: commit-${{ github.sha }}
          name: "Preview Build (${{ github.sha }})"
          body: "自动构建版本 - Commit: ${{ github.sha }}"
          prerelease: true
          artifacts: artifacts/*
          token: ${{ secrets.GITHUB_TOKEN }}

      # ─────────────── 打 TAG 时发布正式版 ───────────────
      - name: 🏷️ Create Tag Release
        if: startsWith(github.ref, 'refs/tags/v')
        uses: ncipollo/release-action@v1.16.0
        with:
          tag: ${{ github.ref_name }}
          name: Release ${{ github.ref_name }}
          artifacts: artifacts/*
          token: ${{ secrets.GITHUB_TOKEN }}