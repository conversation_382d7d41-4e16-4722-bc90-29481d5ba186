import Flutter
import UIKit
import XCTest

class RunnerTests: XCTestCase {

  func testExample() {
      let multiParams = parseAllParameters(from: "https://genconusantara.com/auth/callback?code=XBJaAUsqF8OjMAPfjYxPDB_3njPncfBrM6j92vUM6VjjuWoppZ1wY8BW_g7VV-HA4Ays_JasLWcrrK4BtZvAgFR-IAeKnMB3FAWOUxcNwLDKow0dbYL2zZVOkI57KlA0Beyb2t80_JqlZnMaZ3fomadiHKjNL765GSrxDY9G_XYqJ2823oUsBvy9QrYout402Xr6n5AmY8X9mFGwyysnrj-d1GCn5XDDQKou3d6kIKwG3hc9JlwqQYrD03sRE2fTNxjyQOe3huiYA6gLZqVqEQ4zL-mveAqZnEgOaC4cTko%2A0%215337.va&scopes=user.info.basic&from_platform=tiktokopensdk&request_id=FD1C62B9-73E0-4527-8CA8-22BBA3089D1B&error_code=0&response_id=FD1C62B9-73E0-4527-8CA8-22BBA3089D1B")
      print(multiParams)
  }
    
    func parseAllParameters(from urlString: String) -> [String: [String]] {
        guard let url = URL(string: urlString),
              let components = URLComponents(url: url, resolvingAgainstBaseURL: true),
              let queryItems = components.queryItems else {
            return [:]
        }
        
        var parameters: [String: [String]] = [:]
        for item in queryItems {
            if let value = item.value {
                parameters[item.name, default: []].append(value)
            }
        }
        return parameters
    }

}
