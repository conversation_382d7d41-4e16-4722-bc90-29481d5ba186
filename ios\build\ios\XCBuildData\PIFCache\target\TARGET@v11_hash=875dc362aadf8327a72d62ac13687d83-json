{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988f9b2b47d3bb2f542f094a18345dcd76", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fdaadc44d88c455eb0619708eb38286", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838d79946ab5fedd1c7853346a736c349", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98518f80b424a1452a99f08f5eb87f001a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838d79946ab5fedd1c7853346a736c349", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987a2acf1d5dd190674769b43aade3b4b0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ef39b20b6542590353aa0c1c7981f290", "guid": "bfdfe7dc352907fc980b868725387e98aa5dcae6559b2c73de4a87e5af89c0e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849f1c6740978c631393d690d38a8538c", "guid": "bfdfe7dc352907fc980b868725387e989bc0404094ba75642c4fd347e4865f26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fef506b5e765902d9c2658f456bcbf4c", "guid": "bfdfe7dc352907fc980b868725387e9822db14b0acabe420026db827e35dbd66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c526885eec785a2c39422be6d903093", "guid": "bfdfe7dc352907fc980b868725387e9890da43714ffaa7ce5cd0ed9a94a48430", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d763c88549f6155a13cc95a40b97a280", "guid": "bfdfe7dc352907fc980b868725387e98554cd0f7d1ef5bbfe53b4ee6b86e6668", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e329081eaee612d73eda692a69062d9", "guid": "bfdfe7dc352907fc980b868725387e9880a982d265cd00025419b4f649c69e00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea3e0827c738f293c041aa174ab3eaa", "guid": "bfdfe7dc352907fc980b868725387e98c95ce69a88f97db3115b87b60e1e13fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b056748a3a19a9356de968431a93b43", "guid": "bfdfe7dc352907fc980b868725387e98e6623e747ae4fa0d1b942a974e6aaacb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9860a6b0848bbac40caa52180521a2ea40", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be80ec7bb924fd7b0a8366c59e95e34d", "guid": "bfdfe7dc352907fc980b868725387e988d5760221100e0330e0864714d153857"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eac7725fa1d7333257051f5ac664b75", "guid": "bfdfe7dc352907fc980b868725387e987b3315efa3922d111cd3a82f73ff6a84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875a127622831fc68aeeb8ef47500f8d2", "guid": "bfdfe7dc352907fc980b868725387e98cb78d7a383c9518eda0c457470cd3e0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a95ce3907b8e5dd7a0f7534ffff194a", "guid": "bfdfe7dc352907fc980b868725387e982ea092c4695fec2513997a68e98e54ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fae3af57e9852dac0c5ebdc903838f2", "guid": "bfdfe7dc352907fc980b868725387e98f03e8fdbaa95c6a9834d6cbfe37651ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851af596e003ebeafcef0b6caa8fb8c8d", "guid": "bfdfe7dc352907fc980b868725387e98f97820b8623c37bc08de846f1070b01e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af2035aabb476307c4e792a4ead3bee4", "guid": "bfdfe7dc352907fc980b868725387e989ef721fe72d1e0fedbc70e38f59c8e06"}], "guid": "bfdfe7dc352907fc980b868725387e98ab88ec68ff83b41a197bd8a0101a5f82", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb38708557615e4a040221b216db5a97", "guid": "bfdfe7dc352907fc980b868725387e98f9954b11d65ea226f76856998dcb469b"}], "guid": "bfdfe7dc352907fc980b868725387e983428b9de75e5d7dae87d662434c86ba5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7c65cffc73efe8c128abf2b9316e735", "targetReference": "bfdfe7dc352907fc980b868725387e98082dc85da1fc941e5234c7cc1f11b27d"}], "guid": "bfdfe7dc352907fc980b868725387e986bcb1b60290f89ac6e0e5122dda4291d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98082dc85da1fc941e5234c7cc1f11b27d", "name": "image_picker_ios-image_picker_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981f000f066404b97b12e9c4ca84d38d0f", "name": "image_picker_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988e06e8c3685b7c12032d8059f412f4cb", "name": "image_picker_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}