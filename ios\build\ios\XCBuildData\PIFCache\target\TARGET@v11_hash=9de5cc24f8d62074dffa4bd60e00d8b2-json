{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894f04f1c738b59e475291f164bc57c1d", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "Apple Development", "CODE_SIGN_STYLE": "Automatic", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios", "DEVELOPMENT_TEAM": "5ZT64D7ZTC", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "url_launcher_ios", "INFOPLIST_FILE": "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "url_launcher_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98d06052fb2940602371d3409b909340d7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d7614490f90e28c9c2568b467cc94d58", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "Apple Distribution", "CODE_SIGN_STYLE": "Manual", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios", "DEVELOPMENT_TEAM": "5ZT64D7ZTC", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "url_launcher_ios", "INFOPLIST_FILE": "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "url_launcher_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9879ff122386a197a4fb434f2fed0ac8cb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d7614490f90e28c9c2568b467cc94d58", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "Apple Distribution", "CODE_SIGN_STYLE": "Manual", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios", "DEVELOPMENT_TEAM": "5ZT64D7ZTC", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "url_launcher_ios", "INFOPLIST_FILE": "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "url_launcher_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98cad74fb2ab629138cbd39f6cb9a02130", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985928eb0fd3f74100f39a9b33b26bae03", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988aabc6089a42999cb99eeb6dbef1075e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98efbeaba56b8c1ccd4938b40117905926", "guid": "bfdfe7dc352907fc980b868725387e98e6fd13e149988e2641189b7f5e7b06cd"}], "guid": "bfdfe7dc352907fc980b868725387e98aa1735c14875425f5856d7505c8ad0b2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9891b3b8cc56823cdea4b418e009a423b2", "name": "url_launcher_ios-url_launcher_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9827df8da513ac7d6928fc311b53a7155d", "name": "url_launcher_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}