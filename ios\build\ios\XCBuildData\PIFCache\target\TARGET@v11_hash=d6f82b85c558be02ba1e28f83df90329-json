{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9874235fe094b557c7afbb8c51e6ddc10b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/TikTokOpenAuthSDK/TikTokOpenAuthSDK-prefix.pch", "INFOPLIST_FILE": "Target Support Files/TikTokOpenAuthSDK/TikTokOpenAuthSDK-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TikTokOpenAuthSDK/TikTokOpenAuthSDK.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "TikTokOpenAuthSDK", "PRODUCT_NAME": "TikTokOpenAuthSDK", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9811cbb025c9e824b748b559734d63957d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5d2f19d22c3cf3119b225c95b15095f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/TikTokOpenAuthSDK/TikTokOpenAuthSDK-prefix.pch", "INFOPLIST_FILE": "Target Support Files/TikTokOpenAuthSDK/TikTokOpenAuthSDK-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TikTokOpenAuthSDK/TikTokOpenAuthSDK.modulemap", "PRODUCT_MODULE_NAME": "TikTokOpenAuthSDK", "PRODUCT_NAME": "TikTokOpenAuthSDK", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988436c3816a33ff5a246cee8362e48146", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5d2f19d22c3cf3119b225c95b15095f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/TikTokOpenAuthSDK/TikTokOpenAuthSDK-prefix.pch", "INFOPLIST_FILE": "Target Support Files/TikTokOpenAuthSDK/TikTokOpenAuthSDK-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TikTokOpenAuthSDK/TikTokOpenAuthSDK.modulemap", "PRODUCT_MODULE_NAME": "TikTokOpenAuthSDK", "PRODUCT_NAME": "TikTokOpenAuthSDK", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989fe315851720b99a68539b7a87fa05cc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98687ee2beb63d62300d0f0710676d1186", "guid": "bfdfe7dc352907fc980b868725387e9808dd1773010e367496a3160cb963d980", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983f30a9219699027fa24de028b5ffc8cc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985450aafbac54ecf7ba886563715cebc5", "guid": "bfdfe7dc352907fc980b868725387e98d11798e12e20a85029a54d2bd8597763"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ba2870a6f42794765d2ce7fe0800507", "guid": "bfdfe7dc352907fc980b868725387e98369ae77059ad9b089086d1f66d108170"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b807c1eb9ffe3d0d4af1c9e84a456f", "guid": "bfdfe7dc352907fc980b868725387e98a042d156e0b1c59413f7ee1c7c99145e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db61a6aeb689fed577f56f3003c0658b", "guid": "bfdfe7dc352907fc980b868725387e9869f5392a8501f5b04e455aadaa862d5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8328620de7317d164a81158d886eb6f", "guid": "bfdfe7dc352907fc980b868725387e9894e9fbbc4b142e7f2f7bede240976ac8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823027a1464536ac15b8999fd86c28938", "guid": "bfdfe7dc352907fc980b868725387e987a19102e797b9378c54246bdbbf5f922"}], "guid": "bfdfe7dc352907fc980b868725387e98433fddb5f49021957cd7f182e15c46b5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb38708557615e4a040221b216db5a97", "guid": "bfdfe7dc352907fc980b868725387e9823b4aa46fd22379767a878ced490b4f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98172ba60474b8efc20038a09ae209cbe4", "guid": "bfdfe7dc352907fc980b868725387e983566fbcc53495f8d7aa51af4b7148e9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4bcb52b6ef922983b347a324d1d3070", "guid": "bfdfe7dc352907fc980b868725387e9846130be98a3fad9ba6c7d790ffc56cbc"}], "guid": "bfdfe7dc352907fc980b868725387e98c583ab6970926850ed50b28d48431443", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98066ee16a1295811cbbef4587ed24bb37", "targetReference": "bfdfe7dc352907fc980b868725387e98081d572d9d583a917edaee208b416672"}], "guid": "bfdfe7dc352907fc980b868725387e98e52414e3a49c7ab3e048dca5103362d5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98081d572d9d583a917edaee208b416672", "name": "TikTokOpenAuthSDK-TikTokOpenAuthSDKPrivacyInfo"}, {"guid": "bfdfe7dc352907fc980b868725387e987487e08017d4d7651e52f9beb45fa96b", "name": "TikTokOpenSDKCore"}], "guid": "bfdfe7dc352907fc980b868725387e9851fe6df18a5efc8a1e51476e51e147df", "name": "TikTokOpenAuthSDK", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9875365e74997d98bb10237105dccdb6e7", "name": "TikTokOpenAuthSDK.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}