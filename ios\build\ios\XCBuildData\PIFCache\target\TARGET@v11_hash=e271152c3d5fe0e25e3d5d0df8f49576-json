{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98473fd985496be4d7f58dbf3314cacc11", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/TikTokOpenShareSDK/TikTokOpenShareSDK-prefix.pch", "INFOPLIST_FILE": "Target Support Files/TikTokOpenShareSDK/TikTokOpenShareSDK-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TikTokOpenShareSDK/TikTokOpenShareSDK.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "TikTokOpenShareSDK", "PRODUCT_NAME": "TikTokOpenShareSDK", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c245437bae609e25bccbe63a9e161895", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988bfeb09a8db6457c5f2282dedd020cfb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/TikTokOpenShareSDK/TikTokOpenShareSDK-prefix.pch", "INFOPLIST_FILE": "Target Support Files/TikTokOpenShareSDK/TikTokOpenShareSDK-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TikTokOpenShareSDK/TikTokOpenShareSDK.modulemap", "PRODUCT_MODULE_NAME": "TikTokOpenShareSDK", "PRODUCT_NAME": "TikTokOpenShareSDK", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f85f20ea11cda197ff86990edcea0afd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988bfeb09a8db6457c5f2282dedd020cfb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/TikTokOpenShareSDK/TikTokOpenShareSDK-prefix.pch", "INFOPLIST_FILE": "Target Support Files/TikTokOpenShareSDK/TikTokOpenShareSDK-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TikTokOpenShareSDK/TikTokOpenShareSDK.modulemap", "PRODUCT_MODULE_NAME": "TikTokOpenShareSDK", "PRODUCT_NAME": "TikTokOpenShareSDK", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98051cc23f81049270bc6f8498b914e833", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985fa05897cb59e8e8ef7df3d22d5d4bec", "guid": "bfdfe7dc352907fc980b868725387e98897c46e0a31870c8a47779d91469bd5e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98276900d6bf43fad51533e1fd43df1611", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846c72e21fa486b39206a85de47d6bc70", "guid": "bfdfe7dc352907fc980b868725387e98da00385905be523a8f52b5ffb183dd65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98596dba2da477d2e8073f2643eaa9d875", "guid": "bfdfe7dc352907fc980b868725387e9804b6237ab4f7fa4576fa768629f04401"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808d74a79851b11e9b97b4d5f9334cf54", "guid": "bfdfe7dc352907fc980b868725387e98badcefb94bd26c587d103bbb4786874a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7828e066402c0fa1fad486b35ff01b7", "guid": "bfdfe7dc352907fc980b868725387e98c1c90f0500a38a7a99f40bdfd77c732b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad57b916b7dc1571a0c2c2df134a27c3", "guid": "bfdfe7dc352907fc980b868725387e9894b9dcd4e668a95c7bbd3fcb40ccb4e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843d4d546d1059d99e9e6b894f64fbb59", "guid": "bfdfe7dc352907fc980b868725387e98c3ab5ccdafd3dda23128ad8c1c942d75"}], "guid": "bfdfe7dc352907fc980b868725387e98292fe87408751efcae86b0152376c6f2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb38708557615e4a040221b216db5a97", "guid": "bfdfe7dc352907fc980b868725387e987a5b8b77921ee0d3dccbe3f41e3cf375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98172ba60474b8efc20038a09ae209cbe4", "guid": "bfdfe7dc352907fc980b868725387e982df14161eda4e1e55e30c788cc3530fc"}], "guid": "bfdfe7dc352907fc980b868725387e983093f5ddfd6579584d89e729ec4a3fa7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982c6c3918382d821999d8a1da5a3da676", "targetReference": "bfdfe7dc352907fc980b868725387e98f4278eba82c0076e23013582c0424328"}], "guid": "bfdfe7dc352907fc980b868725387e985de252ccb8ab02fb2a75a1ecc7e41140", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e987487e08017d4d7651e52f9beb45fa96b", "name": "TikTokOpenSDKCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98f4278eba82c0076e23013582c0424328", "name": "TikTokOpenShareSDK-TikTokOpenShareSDKPrivacyInfo"}], "guid": "bfdfe7dc352907fc980b868725387e98754abcb1855749a02eff8970eba88953", "name": "TikTokOpenShareSDK", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a1083aff2014f1228af5bc73f62fffad", "name": "TikTokOpenShareSDK.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}