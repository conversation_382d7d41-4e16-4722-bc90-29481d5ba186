import 'dart:io';

import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/pages/brand/brand_home_notifier.dart';
import 'package:milestone/pages/detail/product_detail_notifier.dart';
import 'package:milestone/pages/home/<USER>';
import 'package:milestone/pages/home_app.dart';
import 'package:milestone/utils/config.dart';
import 'package:milestone/utils/language_manager.dart';
import 'package:milestone/utils/sp.dart';
import 'package:milestone/utils/tiktok_login.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:tiktok_sdk_v2/tiktok_sdk_v2.dart';

import 'controller/event_bus_controller.dart';
import 'models/recharge_config.dart';

void main() {
  Global.init(() async {
    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => LanguageManager()),
          ChangeNotifierProvider(create: (_) => HomeSinglePageNotifier()),
          ChangeNotifierProvider(create: (_) => ProductDetailNotifier()),
          ChangeNotifierProvider(create: (_) => BrandHomeNotifier()),
        ],
        child: const HomeApp(),
      ),
    );
  });
}

class Global {
  static bool isDebugMode = false;
  static String localVersion = '1.0.0';
  static RechargeConfig? rechargeConfig;

  static Future init(VoidCallback callback) async {
    WidgetsFlutterBinding.ensureInitialized();
    await SpUtil.getInstance();
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    localVersion = packageInfo.version;

    // init network initialize checking
    NetworkService();

    dynamic userInfo = UserManager.getUserInfo();
    Logger().d("userInfo: $userInfo");
    String platform = Platform.isAndroid ? "android" : "ios";
    networkApiClient.getAppInfo(platform).then((info) {
      Logger().d("info: $info");
      String version = info["appVersion"];
      if (version.isNotEmpty && version.trim() == localVersion) {
        isDebugMode = info["debug"] == "true";
        Logger().d("debug mode: $isDebugMode");
      }
    });

    if(isLogin()) {
      loadingGlobalInformation();
    }else {
      TiktokLogin.initTikTokLogin();
    }

    eventBus.on<LoginEvent>().listen((event) {
      loadingGlobalInformation();
    });

    await TikTokSDK.instance.setup(clientKey: Config.tiktokClientKey);
    callback();
  }

  static void loadingGlobalInformation({VoidCallback? complete}) {
    networkApiClient.getUserInfo().then((userInfo) {
      Logger().d("userInfo: $userInfo");
      UserManager.saveFullUserInfo(userInfo);
    });

    networkApiClient.getRechargeConfig().then((rechargeConfig) {
      Global.rechargeConfig = rechargeConfig;
      if(complete != null) {
        complete();
      }
    });
  }
}