import 'package:azlistview/azlistview.dart';

List<String> bankNames = [
  "Anglomas International Bank",
  "Bank Mestika Dharma",
  "Bank Syariah Indonesia (BSI)",
  "Bank Oke Indonesia (formerly Bank Andara)",
  "Bank Woori Saudara Indonesia 1906 (formerly Bank Himpunan Saudara and Bank Woori Indonesia)",
  "Bank Amar Indonesia (formerly Anglomas International Bank)",
  "Bank Sinarmas UUS",
  "BTPN Syariah (formerly BTPN UUS and Bank Sahabat Purba Danarta)",
  "Bank JTrust Indonesia (formerly Bank Mutiara)",
  "BPD Banten (formerly Bank Pundi Indonesia)",
  "Bank QNB Indonesia (formerly Bank QNB Kesawan)",
  "Bank Shinhan Indonesia (formerly Bank Metro Express)",
  "Bank Maybank",
  "Bank Negara Indonesia (BNI)",
  "Bank Rakyat Indonesia (BRI)",
  "Bank Arta Niaga Kencana",
  "Bank Mitra Niaga",
  "BPD Sumut UUS",
  "BPD Sumut",
  "BPD Sumsel Dan Babel UUS",
  "BPD Sumsel Dan Babel",
  "BPD Sumatera Barat UUS",
  "BPD Sumatera Barat",
  "BPD Sulut",
  "BPD Sulselbar UUS",
  "BPD Sulselbar",
  "BPD Sulawesi Tenggara",
  "BPD Sulawesi Tengah",
  "BPD Riau Dan Kepri UUS",
  "BPD Riau Dan Kepri",
  "BPD Papua",
  "BPD Nusa Tenggara Timur",
  "BPD Nusa Tenggara Barat UUS",
  "BPD Nusa Tenggara Barat",
  "BPD Maluku",
  "BPD Lampung",
  "BPD Kalimantan Timur UUS",
  "BPD Kalimantan Timur",
  "BPD Kalimantan Tengah",
  "BPD Kalimantan Selatan UUS",
  "BPD Kalimantan Selatan",
  "BPD Kalimantan Barat UUS",
  "BPD Kalimantan Barat",
  "BPD Jawa Timur UUS",
  "BPD Jawa Timur",
  "BPD Jawa Tengah UUS",
  "BPD Jawa Tengah",
  "BPD Jambi UUS",
  "BPD Jambi",
  "BPD Daerah Istimewa Yogyakarta (DIY) UUS",
  "BPD Daerah Istimewa Yogyakarta (DIY)",
  "BPD Bengkulu",
  "BPD Bali",
  "BPD Aceh UUS",
  "BPD Aceh",
  "Bank Yudha Bhakti",
  "Bank Woori Indonesia",
  "Bank Victoria Syariah",
  "Bank Victoria Internasional",
  "Bank UOB Indonesia",
  "Bank Tabungan Pensiunan Nasional",
  "Bank Tabungan Negara (BTN) UUS",
  "Bank Tabungan Negara (BTN)",
  "Bank Syariah Mega",
  "Bank Syariah Mandiri",
  "Bank Syariah Bukopin",
  "Bank Syariah BRI",
  "Bank Sumitomo Mitsui Indonesia",
  "Bank SBI Indonesia",
  "Bank Sahabat Sampoerna",
  "Bank Resona Perdania",
  "Bank Rabobank International Indonesia",
  "Bank Permata UUS",
  "Bank Panin Syariah",
  "Bank of India Indonesia",
  "Bank of China (BOC)",
  "Bank of America Merill - Lynch",
  "Bank OCBC NISP UUS",
  "Bank OCBC NISP",
  "Bank Nusantara Parahyangan",
  "Bank Nationalnobu",
  "Bank Multi Arta Sentosa",
  "Bank Muamalat Indonesia",
  "Bank MNC Internasional",
  "Bank Mizuho Indonesia",
  "Bank Mega",
  "Bank Mayora",
  "Bank Maybank Syariah Indonesia",
  "Bank Mayapada International",
  "Bank Maspion Indonesia",
  "Bank Kesejahteraan Ekonomi",
  "Bank Jasa Jakarta",
  "Bank Index Selindo",
  "Bank Ina Perdania",
  "Bank ICBC Indonesia",
  "Bank Himpunan Saudara 1906",
  "Bank Harda Internasional",
  "Bank Hana",
  "Bank Ganesha",
  "Bank Fama International",
  "Bank DKI UUS",
  "Bank DKI",
  "Bank Dinar Indonesia",
  "Bank DBS Indonesia",
  "Bank Danamon UUS",
  "Bank Commonwealth",
  "Bank CIMB Niaga UUS",
  "Bank Chinatrust Indonesia",
  "Bank Central Asia (BCA) Syariah",
  "Bank Capital Indonesia",
  "Bank Bumi Arta",
  "Bank Bukopin",
  "Bank BNP Paribas",
  "Bank BNI Syariah",
  "Bank BJB Syariah",
  "Bank BJB",
  "Bank Bisnis Internasional",
  "Bank Artos Indonesia",
  "Bank Artha Graha International",
  "Bank ANZ Indonesia",
  "Bank Andara",
  "Bank BRI Agroniaga",
  "Bank Sinarmas",
  "Bank Agris",
  "Bangkok Bank",
  "Bank Panin",
  "Bank Permata",
  "Bank Danamon",
  "Bank CIMB Niaga",
  "Bank Mandiri",
  "Bank Central Asia (BCA)",
  "China Construction Bank Indonesia (formerly Bank Antar Daerah and Bank Windu Kentjana International)",
  "Citibank",
  "Deutsche Bank",
  "Hongkong and Shanghai Bank Corporation (HSBC) UUS",
  "HSBC Indonesia (formerly Bank Ekonomi Raharja)",
  "Indonesia Eximbank (formerly Bank Ekspor Indonesia)",
  "JP Morgan Chase Bank",
  "JP Morgan Chase Bank",  // 注意：原始数据中有重复项
  "LinkAja",
  "Mandiri E-Cash",
  "Mandiri Taspen Pos (formerly Bank Sinar Harapan Bali)",
  "Prima Master Bank",
  "Royal Bank of Scotland (RBS)",
  "Standard Chartered Bank",
];

// 银行信息模型类
class BankNameInfo extends ISuspensionBean {
  final String name;
  final String tagIndex;

  BankNameInfo({
    required this.name,
    required this.tagIndex,
  });

  @override
  String getSuspensionTag() => tagIndex;
}