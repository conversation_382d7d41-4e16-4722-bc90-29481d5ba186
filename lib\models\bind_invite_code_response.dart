class BindInviteCodeResponse {
  final int uid;
  final String nickname;
  final String avatar;
  final dynamic time;
  final int level;
  final int? childCount;
  final int? orderCount;
  final int? numberCount;

  BindInviteCodeResponse({
    required this.uid,
    required this.nickname,
    required this.avatar,
    this.time,
    required this.level,
    this.childCount,
    this.orderCount,
    this.numberCount,
  });

  factory BindInviteCodeResponse.fromJson(Map<String, dynamic> json) {
    return BindInviteCodeResponse(
      uid: json['uid'] as int,
      nickname: json['nickname'] as String,
      avatar: json['avatar'] as String,
      time: json['time'],
      level: json['level'] as int,
      childCount: json['childCount'] as int?,
      orderCount: json['orderCount'] as int?,
      numberCount: json['numberCount'] as int?,
    );
  }

  @override
  String toString() {
    return 'BindInviteCodeResponse{uid: $uid, nickname: $nickname, avatar: $avatar, '
        'time: $time, level: $level, childCount: $childCount, '
        'orderCount: $orderCount, numberCount: $numberCount}';
  }
}