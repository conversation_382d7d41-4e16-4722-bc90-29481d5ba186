import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';

enum BrandProductFilter {
  all,
  price,
  sales,
  latest,
  rebateRate,
}

List<BrandProductFilter> brandProductFilters = [
  BrandProductFilter.all,
  BrandProductFilter.price,
  BrandProductFilter.sales,
  BrandProductFilter.latest,
  BrandProductFilter.rebateRate
];

extension BrandProductFilterDisplayName on BrandProductFilter {
  String displayName(BuildContext context) {
    switch (this) {
      case BrandProductFilter.all:
        return S.of(context).brand_filter_all;
      case BrandProductFilter.price:
        return S.of(context).brand_filter_price;
      case BrandProductFilter.sales:
        return S.of(context).brand_filter_sales;
      case BrandProductFilter.latest:
        return S.of(context).brand_filter_latest;
      case BrandProductFilter.rebateRate:
        return S.of(context).brand_filter_rebate_rate;
    }
  }

  int value() {
    switch (this) {
      case BrandProductFilter.all:
        return 0;
      case BrandProductFilter.price:
        return 1;
      case BrandProductFilter.sales:
        return 2;
      case BrandProductFilter.latest:
        return 3;
      case BrandProductFilter.rebateRate:
        return 4;
    }
  }
}


extension BrandProductFilterTabs on BrandProductFilter {
  Widget tabView(BuildContext context) {
    switch (this) {
      case BrandProductFilter.all:
        return Tab(text: S.of(context).brand_filter_all, height: 30);
      case BrandProductFilter.price:
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Tab(text: S.of(context).brand_filter_price, height: 30),
            buildSortIcon(context),
          ],
        );
      case BrandProductFilter.sales:
        return Tab(text: S.of(context).brand_filter_sales, height: 30);
      case BrandProductFilter.latest:
        return Tab(text: S.of(context).brand_filter_latest, height: 30);
      case BrandProductFilter.rebateRate:
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Tab(text: S.of(context).brand_filter_rebate_rate, height: 30),
            buildSortIcon(context),
          ],
        );
    }
  }

  Widget buildSortIcon(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset(R.assetsImagesIcArrowTop),
        Image.asset(R.assetsImagesIcBrandDown)
      ],
    );
  }
}