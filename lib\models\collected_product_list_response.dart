// models/collected_product_list_response.dart
import 'collected_product_item.dart';

class CollectedProductListResponse {
  final int page;
  final int limit;
  final int totalPage;
  final int total;
  final List<CollectedProductItem> list;

  CollectedProductListResponse({
    required this.page,
    required this.limit,
    required this.totalPage,
    required this.total,
    required this.list,
  });

  factory CollectedProductListResponse.fromJson(Map<String, dynamic> json) {
    var list = (json['list'] as List)
        .map((e) => CollectedProductItem.fromJson(e))
        .toList();
    return CollectedProductListResponse(
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 10,
      totalPage: json['totalPage'] ?? 1,
      total: json['total'] ?? 0,
      list: list,
    );
  }
}
