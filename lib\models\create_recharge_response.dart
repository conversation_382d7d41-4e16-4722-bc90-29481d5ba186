// models/create_recharge_response.dart
class CreateRechargeResponse {
  final String orderNo;
  final bool status;
  final String payType;
  final String amount;
  final dynamic jsConfig;      // 不确定类型，保持为dynamic
  final dynamic alipayRequest; // 不确定类型，保持为dynamic
  final dynamic aliPayConfig;  // 不确定类型，保持为dynamic

  CreateRechargeResponse({
    required this.orderNo,
    required this.status,
    required this.payType,
    required this.amount,
    this.jsConfig,
    this.alipayRequest,
    this.aliPayConfig,
  });

  factory CreateRechargeResponse.fromJson(Map<String, dynamic> json) {
    return CreateRechargeResponse(
      orderNo: json['orderNo'] as String? ?? '',
      status: json['status'] as bool? ?? false,
      payType: json['payType'] as String? ?? '',
      amount: json['amount'] as String? ?? '',
      jsConfig: json['jsConfig'],
      alipayRequest: json['alipayRequest'],
      aliPayConfig: json['aliPayConfig'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'orderNo': orderNo,
      'status': status,
      'payType': payType,
      'amount': amount,
      'jsConfig': jsConfig,
      'alipayRequest': alipayRequest,
      'aliPayConfig': aliPayConfig,
    };
  }
}