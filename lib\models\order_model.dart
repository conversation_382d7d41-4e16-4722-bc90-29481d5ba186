// models/order_model.dart
import 'dart:ui';

import 'package:flutter/material.dart';

import 'order_product_info_model.dart';

class OrderModel {
  final String? storeOrder;
  final String? cartInfo;
  final String? statusPic;
  final String? offlinePayStatus;
  final String? id;
  final String? orderId;
  final String? createTime;
  final bool paid;
  final String? payTime;
  final String? payPrice;
  final int status;
  final String? orderStatus;
  final String? totalNum;
  final String? payPostage;
  final String? refundStatus;
  final String? deliveryName;
  final String? deliveryType;
  final String? deliveryId;
  final String? pinkId;
  final String? bargainId;
  final String? verifyCode;
  final String? storeId;
  final String? shippingType;
  final String? activityType;
  final String? type;
  final List<OrderProductInfoModel> orderInfoList;

  OrderModel({
    this.storeOrder,
    this.cartInfo,
    this.statusPic,
    this.offlinePayStatus,
    this.id,
    this.orderId,
    this.createTime,
    required this.paid,
    this.payTime,
    this.payPrice,
    this.status = 0,
    this.orderStatus,
    this.totalNum,
    this.payPostage,
    this.refundStatus,
    this.deliveryName,
    this.deliveryType,
    this.deliveryId,
    this.pinkId,
    this.bargainId,
    this.verifyCode,
    this.storeId,
    this.shippingType,
    this.activityType,
    this.type,
    required this.orderInfoList,
  });

  Color statusColor() {
    switch (this.status) {
      case 0:
        return const Color(0xFFF5A623); // 黄色
      case 1:
        return const Color(0xFF26C266); // 绿色
      case 2:
        return const Color(0xFFAAAAAA); // 灰色
      default:
        return Colors.black;
    }
  }

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    var orderItems = (json['orderInfoList'] as List? ?? [])
        .map((e) => OrderProductInfoModel.fromJson(e))
        .toList();
    return OrderModel(
      storeOrder: json['storeOrder']?.toString(),
      cartInfo: json['cartInfo']?.toString(),
      statusPic: json['statusPic']?.toString(),
      offlinePayStatus: json['offlinePayStatus']?.toString(),
      id: json['id']?.toString(),
      orderId: json['orderId']?.toString(),
      createTime: json['createTime']?.toString(),
      paid: json['paid'] ?? false,
      payTime: json['payTime']?.toString(),
      payPrice: json['payPrice']?.toString(),
      status: json['status'],
      orderStatus: json['orderStatus']?.toString(),
      totalNum: json['totalNum']?.toString(),
      payPostage: json['payPostage']?.toString(),
      refundStatus: json['refundStatus']?.toString(),
      deliveryName: json['deliveryName']?.toString(),
      deliveryType: json['deliveryType']?.toString(),
      deliveryId: json['deliveryId']?.toString(),
      pinkId: json['pinkId']?.toString(),
      bargainId: json['bargainId']?.toString(),
      verifyCode: json['verifyCode']?.toString(),
      storeId: json['storeId']?.toString(),
      shippingType: json['shippingType']?.toString(),
      activityType: json['activityType']?.toString(),
      type: json['type']?.toString(),
      orderInfoList: orderItems,
    );
  }
}
