// models/order_product_info_model.dart
class OrderProductInfoModel {
  final String? attrId;
  final String? productId;
  final String? cartNum;
  final String? image;
  final String? storeName;
  final String? price;
  final String? isReply;
  final String? sku;
  final String? commissionRate;
  final String? actualCommission;
  final String? estimatedCommission;

  OrderProductInfoModel({
    this.attrId,
    this.productId,
    this.cartNum,
    this.image,
    this.storeName,
    this.price,
    this.isReply,
    this.sku,
    this.commissionRate,
    this.actualCommission,
    this.estimatedCommission,
  });

  factory OrderProductInfoModel.fromJson(Map<String, dynamic> json) {
    return OrderProductInfoModel(
      attrId: json['attrId']?.toString(),
      productId: json['productId']?.toString(),
      cartNum: json['cartNum']?.toString(),
      image: json['image']?.toString(),
      storeName: json['storeName']?.toString(),
      price: json['price']?.toString(),
      isReply: json['isReply']?.toString(),
      sku: json['sku']?.toString(),
      commissionRate: json['commissionRate']?.toString(),
      actualCommission: json['actualCommission']?.toString(),
      estimatedCommission: json['estimatedCommission']?.toString(),
    );
  }
}
