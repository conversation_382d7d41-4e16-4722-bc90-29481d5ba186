import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';

enum OrderStatus {
  all,
  processing,
  completed,
  expired
}

List<OrderStatus> orderStatusList = [
  OrderStatus.all,
  OrderStatus.processing,
  OrderStatus.completed,
  OrderStatus.expired
];

extension OrderStatusDisplay on OrderStatus {
  String displayName(BuildContext context) {
    switch(this) {
      case OrderStatus.all: return S.of(context).order_tab_all;
      case OrderStatus.processing: return S.of(context).order_tab_processing;
      case OrderStatus.completed: return S.of(context).order_tab_completed;
      case OrderStatus.expired: return S.of(context).order_tab_expired;
    }
  }

  int? type() {
    switch(this) {
      case OrderStatus.all: return null;
      case OrderStatus.processing: return 0;
      case OrderStatus.completed: return 1;
      case OrderStatus.expired: return 2;
    }
  }

  Color statusColor() {
    switch (this) {
      case OrderStatus.processing:
        return const Color(0xFFF5A623); // 黄色
      case OrderStatus.completed:
        return const Color(0xFF26C266); // 绿色
      case OrderStatus.expired:
        return const Color(0xFFAAAAAA); // 灰色
      default:
        return Colors.black;
    }
  }
}