class CreatePayRequest {
  final String payType;
  final String type;
  final String reusability;
  final Customer customer;
  final Ewallet ewallet;
  final Map<String, dynamic>? metadata;
  final String amount;
  final String currency;
  final String orderDescription;

  CreatePayRequest({
    required this.payType,
    required this.type,
    required this.reusability,
    required this.customer,
    required this.ewallet,
    this.metadata,
    required this.amount,
    required this.currency,
    required this.orderDescription,
  });

  Map<String, dynamic> toJson() => {
    'pay_type': payType,
    'type': type,
    'reusability': reusability,
    'customer': customer.toJson(),
    'ewallet': ewallet.toJson(),
    if (metadata != null) 'metadata': metadata,
    'amount': amount,
    'currency': currency,
    'order_description': orderDescription,
  };
}

class Customer {
  final String referenceId;
  final String type;
  final IndividualDetail individualDetail;

  Customer({
    required this.referenceId,
    required this.type,
    required this.individualDetail,
  });

  Map<String, dynamic> to<PERSON><PERSON>() => {
    'reference_id': referenceId,
    'type': type,
    'individual_detail': individualDetail.toJson(),
  };
}

class IndividualDetail {
  final String givenNames;
  final String surname;

  IndividualDetail({
    required this.givenNames,
    required this.surname,
  });

  Map<String, dynamic> toJson() => {
    'given_names': givenNames,
    'surname': surname,
  };
}

class Ewallet {
  final String channelCode;
  final ChannelProperties channelProperties;

  Ewallet({
    required this.channelCode,
    required this.channelProperties,
  });

  Map<String, dynamic> toJson() => {
    'channel_code': channelCode,
    'channel_properties': channelProperties.toJson(),
  };
}

class ChannelProperties {
  final String successReturnUrl;
  final String failureReturnUrl;
  final String cancelReturnUrl;

  ChannelProperties({
    required this.successReturnUrl,
    required this.failureReturnUrl,
    required this.cancelReturnUrl,
  });

  Map<String, dynamic> toJson() => {
    'success_return_url': successReturnUrl,
    'failure_return_url': failureReturnUrl,
    'cancel_return_url': cancelReturnUrl,
  };
}

class CreatePayResponse {
  final int status;
  final PaymentData data;

  CreatePayResponse({
    required this.status,
    required this.data,
  });

  factory CreatePayResponse.fromJson(Map<String, dynamic> json) {
    return CreatePayResponse(
      status: json['status'],
      data: PaymentData.fromJson(json['data']),
    );
  }
}

class PaymentData {
  final List<PaymentAction> actions;
  final double amount;
  final String businessId;
  final String id;
  final String status;

  PaymentData({
    required this.actions,
    required this.amount,
    required this.businessId,
    required this.id,
    required this.status,
  });

  factory PaymentData.fromJson(Map<String, dynamic> json) {
    return PaymentData(
      actions: List<PaymentAction>.from(
          json['actions'].map((x) => PaymentAction.fromJson(x))),
      amount: (json['amount'] is double)
          ? json['amount']
          : (json['amount'] as num).toDouble(),
      businessId: json['business_id'],
      id: json['id'],
      status: json['status'],
    );
  }
}

class PaymentAction {
  final String action;
  final String method;
  final String? qrCode;
  final String url;
  final String urlType;

  PaymentAction({
    required this.action,
    required this.method,
    this.qrCode,
    required this.url,
    required this.urlType,
  });

  factory PaymentAction.fromJson(Map<String, dynamic> json) {
    return PaymentAction(
      action: json['action'],
      method: json['method'],
      qrCode: json['qr_code'],
      url: json['url'],
      urlType: json['url_type'],
    );
  }
}