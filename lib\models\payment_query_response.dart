class PaymentQueryResponse {
  final String orderNo;
  final String? outTradeNo;
  final bool paid;
  final double amount;
  final String payChannel;
  final DateTime? payTime;
  final String? transactionId;
  final String bizType;

  PaymentQueryResponse({
    required this.orderNo,
    required this.outTradeNo,
    required this.paid,
    required this.amount,
    required this.payChannel,
    this.payTime,
    this.transactionId,
    required this.bizType,
  });

  factory PaymentQueryResponse.fromJson(Map<String, dynamic> json) {
    return PaymentQueryResponse(
      orderNo: json['orderNo'] as String,
      outTradeNo: json['outTradeNo'] as String,
      paid: json['paid'] as bool,
      amount: double.parse(json['amount'].toString()),
      payChannel: json['payChannel'] as String,
      payTime: json['payTime'] != null
          ? DateTime.parse(json['payTime'] as String)
          : null,
      transactionId: json['transactionId'] as String?,
      bizType: json['bizType'] as String,
    );
  }
}