// models/payment_response.dart
class PaymentResponse {
  final String orderNo;
  final bool status;
  final String payType;
  final String amount;
  final dynamic jsConfig;
  final dynamic alipayRequest;
  final dynamic aliPayConfig;
  final String? payUrl;
  final dynamic qrCode;
  final String? outTradeNo;
  final String? payChannel;

  PaymentResponse({
    required this.orderNo,
    required this.status,
    required this.payType,
    required this.amount,
    this.jsConfig,
    this.alipayRequest,
    this.aliPayConfig,
    this.payUrl,
    this.qrCode,
    this.outTradeNo,
    this.payChannel,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      orderNo: json['orderNo'] as String? ?? '',
      status: json['status'] as bool? ?? false,
      payType: json['payType'] as String? ?? '',
      amount: json['amount'] as String? ?? '',
      jsConfig: json['jsConfig'],
      alipayRequest: json['alipayRequest'],
      aliPayConfig: json['aliPayConfig'],
      payUrl: json['payUrl'] as String?,
      qrCode: json['qrCode'],
      outTradeNo: json['outTradeNo'] as String?,
      payChannel: json['payChannel'] as String?,
    );
  }
}