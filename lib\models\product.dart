class TikTokProduct {
  final String id;              // 商品唯一ID
  final String title;           // 商品标题
  final String shortDescription; // 短描述
  final String? fullDescription; // 完整描述
  final double price;          // 基础价格
  final double? discountPrice; // 折扣价
  final String currency;       // 货币类型
  final double commissionRate; // 佣金比例 (0.15 = 15%)
  final double maxRebateAmount;// 最高返现金额
  final String mainImage;      // 主图URL
  final List<String> galleryImages; // 多图列表
  final String? videoUrl;       // 商品视频URL
  final String category;       // 主分类
  final List<String> tags;      // 标签列表
  final String shopId;         // 店铺ID
  final String shopName;       // 店铺名称
  final double shopRating;     // 店铺评分 (0-5)
  final String affiliateLink;  // 分销链接
  final String pid;            // 推广ID
  final int salesCount;        // 已售数量
  final int shareCount;        // 分享次数
  final int stock;             // 库存数量
  final String status;         // 状态: active/out_of_stock/deleted
  final DateTime createdAt;    // 创建时间
  final DateTime updatedAt;    // 更新时间

  TikTokProduct({
    required this.id,
    required this.title,
    required this.shortDescription,
    this.fullDescription,
    required this.price,
    this.discountPrice,
    this.currency = 'USD',
    required this.commissionRate,
    required this.maxRebateAmount,
    required this.mainImage,
    required this.galleryImages,
    this.videoUrl,
    required this.category,
    required this.tags,
    required this.shopId,
    required this.shopName,
    required this.shopRating,
    required this.affiliateLink,
    required this.pid,
    required this.salesCount,
    required this.shareCount,
    required this.stock,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  // 是否为折扣商品
  bool get isOnDiscount => discountPrice != null;

  // 佣金标签文本
  String get commissionLabel => '${(commissionRate * 100).toStringAsFixed(0)}%';

  // 返现金额标签
  String get rebateLabel => '${maxRebateAmount.toStringAsFixed(2)} $currency';
}