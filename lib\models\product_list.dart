class ProductListResponse {
  final int page;
  final int limit;
  final int totalPage;
  final int total;
  final List<Product> list;

  ProductListResponse({
    required this.page,
    required this.limit,
    required this.totalPage,
    required this.total,
    required this.list,
  });

  factory ProductListResponse.fromJson(Map<String, dynamic> json) {
    return ProductListResponse(
      page: json['page'] as int,
      limit: json['limit'] as int,
      totalPage: json['totalPage'] as int,
      total: json['total'] as int,
      list: (json['list'] as List)
          .map((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      'totalPage': totalPage,
      'total': total,
      'list': list.map((e) => e.toJson()).toList(),
    };
  }
}

class Product {
  final int id;
  final String shopName;
  final String title;
  final String mainImageUrl;
  final String? detailLink;
  final String salesPrice;
  final String? minSalesPrice;
  final String? maxSalesPrice;
  final String? oriPrice;
  final String? minOriPrice;
  final String? maxOriPrice;
  final String channel;
  final String cashBackRate;
  final String cashBackAmount;
  final String? unitsSold;
  final String? unitName;
  final bool? hasInventory;

  Product({
    required this.id,
    required this.shopName,
    required this.title,
    required this.mainImageUrl,
    this.detailLink,
    required this.salesPrice,
    this.minSalesPrice,
    this.maxSalesPrice,
    this.oriPrice,
    this.minOriPrice,
    this.maxOriPrice,
    required this.channel,
    required this.cashBackRate,
    required this.cashBackAmount,
    this.unitsSold,
    this.unitName,
    this.hasInventory,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as int? ?? 0, // 添加空值处理
      shopName: json['shopName'] as String? ?? '', // 添加空值处理
      title: json['title'] as String? ?? '', // 添加空值处理
      mainImageUrl: json['mainImageUrl'] as String? ?? '', // 添加空值处理
      detailLink: json['detailLink'] as String?,
      salesPrice: json['salesPrice'] as String? ?? '0', // 添加空值处理
      minSalesPrice: json['minSalesPrice'] as String?,
      maxSalesPrice: json['maxSalesPrice'] as String?,
      oriPrice: json['oriPrice'] as String?,
      minOriPrice: json['minOriPrice'] as String?,
      maxOriPrice: json['maxOriPrice'] as String?,
      channel: json['channel'] as String? ?? '', // 添加空值处理
      cashBackRate: json['cashBackRate'] as String? ?? '0', // 添加空值处理
      cashBackAmount: json['cashBackAmount']?.toString() ?? '0',
      unitsSold: json['unitsSold'] as String?,
      unitName: json['unitName'] as String?,
      hasInventory: json['hasInventory'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'shopName': shopName,
      'title': title,
      'mainImageUrl': mainImageUrl,
      'detailLink': detailLink,
      'salesPrice': salesPrice,
      'minSalesPrice': minSalesPrice,
      'maxSalesPrice': maxSalesPrice,
      'oriPrice': oriPrice,
      'minOriPrice': minOriPrice,
      'maxOriPrice': maxOriPrice,
      'channel': channel,
      'cashBackRate': cashBackRate,
      'cashBackAmount': cashBackAmount,
      'unitsSold': unitsSold,
      'unitName': unitName,
      'hasInventory': hasInventory,
    };
  }

  // 实用方法：获取价格数值
  double get salesPriceValue {
    try {
      return double.parse(salesPrice);
    } catch (e) {
      return 0.0;
    }
  }

  // 实用方法：获取返现金额
  double get cashBackAmountValue {
    try {
      return double.parse(cashBackAmount);
    } catch (e) {
      return 0.0;
    }
  }
}