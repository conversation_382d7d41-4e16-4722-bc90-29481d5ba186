class TeamSummary {
  final double? estimatedCommission;
  final double? estimatedCommissionToday;
  final double? actualCommission;
  final double? actualCommissionToday;
  final double? inviteReward;
  final double? inviteRewardToday;
  final double? shoppingReward;
  final double? shoppingRewardToday;
  final int? inviteCount;
  final int? inviteCountToday;
  final int? inviteLevel1Count;
  final int? inviteLevel2Count;
  final int? inviteLevel3Count;
  final int? inviteNormalCount;

  TeamSummary({
    this.estimatedCommission,
    this.estimatedCommissionToday,
    this.actualCommission,
    this.actualCommissionToday,
    this.inviteReward,
    this.inviteRewardToday,
    this.shoppingReward,
    this.shoppingRewardToday,
    this.inviteCount,
    this.inviteCountToday,
    this.inviteLevel1Count,
    this.inviteLevel2Count,
    this.inviteLevel3Count,
    this.inviteNormalCount,
  });

  factory TeamSummary.fromJson(Map<String, dynamic> json) {
    return TeamSummary(
      estimatedCommission: _toDouble(json['estimatedCommission']),
      estimatedCommissionToday: _toDouble(json['estimatedCommissionToday']),
      actualCommission: _toDouble(json['actualCommission']),
      actualCommissionToday: _toDouble(json['actualCommissionToday']),
      inviteReward: _toDouble(json['inviteReward']),
      inviteRewardToday: _toDouble(json['inviteRewardToday']),
      shoppingReward: _toDouble(json['shoppingReward']),
      shoppingRewardToday: _toDouble(json['shoppingRewardToday']),
      inviteCount: json['inviteCount'],
      inviteCountToday: json['inviteCountToday'],
      inviteLevel1Count: json['inviteLevel1Count'],
      inviteLevel2Count: json['inviteLevel2Count'],
      inviteLevel3Count: json['inviteLevel3Count'],
      inviteNormalCount: json['inviteNormalCount'],
    );
  }

  static double? _toDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  @override
  String toString() {
    return 'TeamSummary(\n'
        '  estimatedCommission: $estimatedCommission,\n'
        '  estimatedCommissionToday: $estimatedCommissionToday,\n'
        '  actualCommission: $actualCommission,\n'
        '  actualCommissionToday: $actualCommissionToday,\n'
        '  inviteReward: $inviteReward,\n'
        '  inviteRewardToday: $inviteRewardToday,\n'
        '  shoppingReward: $shoppingReward,\n'
        '  shoppingRewardToday: $shoppingRewardToday,\n'
        '  inviteCount: $inviteCount,\n'
        '  inviteCountToday: $inviteCountToday,\n'
        '  inviteLevel1Count: $inviteLevel1Count,\n'
        '  inviteLevel2Count: $inviteLevel2Count,\n'
        '  inviteLevel3Count: $inviteLevel3Count,\n'
        '  inviteNormalCount: $inviteNormalCount,\n'
        ')';
  }
}