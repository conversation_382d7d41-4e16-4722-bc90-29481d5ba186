// models/transaction_detail_model.dart
import 'transaction_group.dart';

class TransactionDetailModel {
  final int page;
  final int limit;
  final int totalPage;
  final int total;
  final List<TransactionGroup> list;

  TransactionDetailModel({
    required this.page,
    required this.limit,
    required this.totalPage,
    required this.total,
    required this.list,
  });

  factory TransactionDetailModel.fromJson(Map<String, dynamic> json) {
    var groups = (json['list'] as List)
        .map((e) => TransactionGroup.fromJson(e))
        .toList();
    return TransactionDetailModel(
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 20,
      totalPage: json['totalPage'] ?? 1,
      total: json['total'] ?? 0,
      list: groups,
    );
  }
}
