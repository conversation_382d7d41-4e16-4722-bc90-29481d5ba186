// models/transaction_item.dart
class TransactionItem {
  final String id;
  final String title;
  final String type;
  final String number;
  final String balance;
  final String? updateTime;
  final String? addTime;

  TransactionItem({
    required this.id,
    required this.title,
    required this.type,
    required this.number,
    required this.balance,
    this.updateTime,
    this.addTime,
  });

  factory TransactionItem.fromJson(Map<String, dynamic> json) {
    return TransactionItem(
      id: json['id'] ?? "",
      title: json['title'] ?? '',
      type: json['type'] ?? '',
      number: json['number'] ?? '0',
      balance: json['balance'] ?? '0',
      updateTime: json['updateTime'],
      addTime: json['add_time'],
    );
  }
}
