class UserInfo {
  final String nickname;
  final String avatar;
  final String phone;
  final String nowMoney;
  final int integral;
  final int experience;
  final String brokeragePrice;
  final int level;
  final bool isPromoter;
  final int couponCount;
  final bool vip;
  final String vipIcon;
  final String vipName;
  final bool rechargeSwitch;
  final int collectCount;
  final String inviteCode;
  final int? spreadUid;
  final String? spreadNickName;
  final String? spreadPhone; // 新增字段

  UserInfo({
    required this.nickname,
    required this.avatar,
    required this.phone,
    required this.nowMoney,
    required this.integral,
    required this.experience,
    required this.brokeragePrice,
    required this.level,
    required this.isPromoter,
    required this.couponCount,
    required this.vip,
    required this.vipIcon,
    required this.vipName,
    required this.rechargeSwitch,
    required this.collectCount,
    required this.inviteCode,
    this.spreadUid,
    this.spreadNickName,
    this.spreadPhone, // 新增字段
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'] ?? '',
      phone: json['phone'] ?? '',
      nowMoney: json['nowMoney'] ?? '0.00',
      integral: json['integral'] ?? 0,
      experience: json['experience'] ?? 0,
      brokeragePrice: json['brokeragePrice'] ?? '0.00',
      level: json['level'] ?? 0,
      isPromoter: json['isPromoter'] ?? false,
      couponCount: json['couponCount'] ?? 0,
      vip: json['vip'] ?? false,
      vipIcon: json['vipIcon'] ?? '',
      vipName: json['vipName'] ?? '',
      rechargeSwitch: json['rechargeSwitch'] ?? false,
      collectCount: json['collectCount'] ?? 0,
      inviteCode: json['inviteCode'] ?? '',
      spreadUid: json['spreadUid'],
      spreadNickName: json['spreadNickName'],
      spreadPhone: json['spreadPhone'], // 新增字段
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'nickname': nickname,
      'avatar': avatar,
      'phone': phone,
      'nowMoney': nowMoney,
      'integral': integral,
      'experience': experience,
      'brokeragePrice': brokeragePrice,
      'level': level,
      'isPromoter': isPromoter,
      'couponCount': couponCount,
      'vip': vip,
      'vipIcon': vipIcon,
      'vipName': vipName,
      'rechargeSwitch': rechargeSwitch,
      'collectCount': collectCount,
      'inviteCode': inviteCode,
      'spreadUid': spreadUid,
      'spreadNickName': spreadNickName,
      'spreadPhone': spreadPhone, // 新增字段
    };
  }

  UserInfo copyWith({
    String? nickname,
    String? avatar,
    String? phone,
    String? nowMoney,
    int? integral,
    int? experience,
    String? brokeragePrice,
    int? level,
    bool? isPromoter,
    int? couponCount,
    bool? vip,
    String? vipIcon,
    String? vipName,
    bool? rechargeSwitch,
    int? collectCount,
    String? inviteCode,
    int? spreadUid,
    String? spreadNickName,
    String? spreadPhone, // 新增字段
  }) {
    return UserInfo(
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      nowMoney: nowMoney ?? this.nowMoney,
      integral: integral ?? this.integral,
      experience: experience ?? this.experience,
      brokeragePrice: brokeragePrice ?? this.brokeragePrice,
      level: level ?? this.level,
      isPromoter: isPromoter ?? this.isPromoter,
      couponCount: couponCount ?? this.couponCount,
      vip: vip ?? this.vip,
      vipIcon: vipIcon ?? this.vipIcon,
      vipName: vipName ?? this.vipName,
      rechargeSwitch: rechargeSwitch ?? this.rechargeSwitch,
      collectCount: collectCount ?? this.collectCount,
      inviteCode: inviteCode ?? this.inviteCode,
      spreadUid: spreadUid ?? this.spreadUid,
      spreadNickName: spreadNickName ?? this.spreadNickName,
      spreadPhone: spreadPhone ?? this.spreadPhone, // 新增字段
    );
  }

  @override
  String toString() {
    return 'UserInfo{'
        'nickname: $nickname, '
        'avatar: $avatar, '
        'phone: $phone, '
        'nowMoney: $nowMoney, '
        'integral: $integral, '
        'experience: $experience, '
        'brokeragePrice: $brokeragePrice, '
        'level: $level, '
        'isPromoter: $isPromoter, '
        'couponCount: $couponCount, '
        'vip: $vip, '
        'vipIcon: $vipIcon, '
        'vipName: $vipName, '
        'rechargeSwitch: $rechargeSwitch, '
        'collectCount: $collectCount, '
        'inviteCode: $inviteCode, '
        'spreadUid: $spreadUid, '
        'spreadNickName: $spreadNickName, '
        'spreadPhone: $spreadPhone' // 新增字段
        '}';
  }
}