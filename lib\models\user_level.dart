import 'package:flutter/cupertino.dart';

import '../generated/assets.dart';
import '../generated/l10n.dart';
import '../r.dart';

String memberLevelName(int memberLevel) {
  switch(memberLevel) {
    case 0: return "Pengguna biasa";
    case 1: return "Agen Perak";
    case 2: return "Agen Emas";
    case 3: return "Agen Berlian";
    case 4: return "Agen Perak";
    case 5: return "Agen Emas";
    case 6: return "Agen Berlian";
    default: return "Pengguna biasa";
  }
}

String memberLevelPublicName(BuildContext context, int memberLevel) {
  switch(memberLevel) {
    case 0: return S.of(context).normal_user;
    case 1: return S.of(context).silver_agent;
    case 2: return S.of(context).gold_agent;
    case 3: return S.of(context).diamond_agent;
    case 4: return S.of(context).silver_partner;
    case 5: return S.of(context).gold_partner;
    case 6: return S.of(context).diamond_partner;
    default: return S.of(context).normal_user;
  }
}

String memberLevelBackgroundImage(int memberLevel) {
  switch(memberLevel) {
    case 0: return R.assetsImagesBgMemberNormal;
    case 1: return R.assetsImagesBgMemberAgentSilver;
    case 2: return R.assetsImagesBgMemberAgentGold;
    case 3: return R.assetsImagesBgMemberAgentDiamand;
    case 4: return R.assetsImagesBgMemberPartnerSilver;
    case 5: return R.assetsImagesBgMemberPartnerGold;
    case 6: return R.assetsImagesBgMemberPartnerDiamand;
    default: return R.assetsImagesBgMemberNormal;
  }
}

String memberLevelBadge(int memberLevel) {
  switch(memberLevel) {
    case 0 : return Assets.imagesIcNormalUser;
    case 1: return Assets.imagesIcSilverAgent;
    case 2: return Assets.imagesIcGoldAgent;
    case 3: return Assets.imagesIcDiamondAgent;
    case 4: return Assets.imagesIcSilverAgent;
    case 5: return Assets.imagesIcGoldAgent;
    case 6: return Assets.imagesIcDiamondAgent;
    default: return Assets.imagesIcNormalUser;
  }
}

enum AgentFilters {
  all, agent, normal
}

extension AgentFiltersNames on AgentFilters {
  String displayName(BuildContext context) {
    switch(this) {
      case AgentFilters.all: return S.of(context).all;
      case AgentFilters.agent: return S.of(context).agent;
      case AgentFilters.normal: return S.of(context).normal_user;
    }
    return "";
  }
}