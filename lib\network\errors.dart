// 1. 定义统一错误处理类
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:milestone/pages/login/login_container.dart';
import 'package:milestone/pages/login/login_with_whatsapp_screen.dart';
import 'package:milestone/utils/language_manager.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/utils/user_utils.dart';

import '../generated/l10n.dart';
import '../main.dart';
import '../pages/login/login_with_tiktok_screen.dart';

class ApiErrorHandler {
  static void handleError(dynamic error, BuildContext? context) {
    if (error is ApiException) {
      final code = error.code;
      final message = error.message;

      if (code == 401 && context != null && context.mounted) {
        // 处理401跳转
        _handleUnauthorized(context);
      } else {
        makeToast(message);
      }
    } else if (error is DioException) {
      // 处理Dio原生错误
      makeToast('Network Error: ${error.message}');
    } else {
      makeToast('Unknown Error: $error');
    }
  }

  static void _handleUnauthorized(BuildContext context) {
    UserManager.clearUserInfo();
    Navigator.push(
      context,
      MaterialPageRoute(
        // fullscreenDialog: true,
        builder: (_) => LoginContainer(),
      ),
    );
    makeToast(S.of(context).login_expired_hint);
  }
}

// 2. 定义自定义异常类
class ApiException implements Exception {
  final int code;
  final String message;

  ApiException(this.code, this.message);

  @override
  String toString() => 'ApiException(code: $code, message: $message)';
}
