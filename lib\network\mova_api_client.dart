// mova_api_client.dart
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../models/brand.dart';

MovaApiClient movaApiClient = MovaApiClient();

class MovaApiClient {
  final String baseUrl = "https://api.mova-shop.com";
  late Dio apiClient;

  MovaApiClient() {
    apiClient = Dio()
      ..options.baseUrl = baseUrl
      ..options.contentType = Headers.formUrlEncodedContentType
      ..options.headers = {"Accept": "application/json"};

    if (kDebugMode) {
      apiClient.interceptors
          .add(LogInterceptor(responseBody: true, requestBody: true));
    }
  }

  Future<BrandPageResponse> getBrandCategories() async {
    try {
      final response = await apiClient.get('/m/api/brand_category/page');
      if (response.statusCode == 200) {
        return BrandPageResponse.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: 'API请求失败，状态码：${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      final statusCode = e.response?.statusCode;
      final errorData = e.response?.data?['msg'] ?? e.message;
      throw Exception('品牌分类请求失败: ${statusCode != null ? '[$statusCode] ' : ''}$errorData');
    }
  }
}