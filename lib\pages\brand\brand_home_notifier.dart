import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:milestone/models/brand.dart';
import 'package:milestone/network/mova_api_client.dart';
import 'package:milestone/network/network_api_client.dart';

import '../../models/category.dart';

class BrandHomeNotifier extends ChangeNotifier {
  bool initLoading = false;
  ProductCategoryListResponse? tiktokBrandPageResponse;
  ProductCategoryListResponse? highRebaseBrandPageResponse;

  Future<void> startLoadingBrandCategory() async {
    initLoading = true;
    notifyListeners();
    try {
      tiktokBrandPageResponse = await networkApiClient.getCategoryList(0);
      Logger().d("tiktokBrandPageResponse:${tiktokBrandPageResponse}");
      highRebaseBrandPageResponse = await networkApiClient.getCategoryList(1);
    } catch (e) {
      debugPrint('Error fetching brand categories: $e');
    } finally {
      initLoading = false;
      notifyListeners();
    }
  }
}