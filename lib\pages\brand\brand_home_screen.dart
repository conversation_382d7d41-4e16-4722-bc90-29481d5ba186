import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/brand.dart';
import 'package:milestone/models/category.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/navigation_route.dart';
import 'package:milestone/widget/empty_view.dart';
import 'package:milestone/widget/image_widget.dart';
import 'package:milestone/widget/skeleton_widget.dart';
import 'package:milestone/widget/text_with_markup.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../controller/network_refresh_mixin.dart';
import '../../controller/refresh_listener_mixin.dart';
import 'brand_home_notifier.dart';
import 'brand_product_list_screen.dart';

class BrandHomeScreen extends StatefulWidget {
  const BrandHomeScreen({super.key});

  @override
  State<StatefulWidget> createState() => _BrandHomeScreenState();
}

class _BrandHomeScreenState extends State<BrandHomeScreen> with AutomaticKeepAliveClientMixin, NetworkRefreshMixin, RefreshListenerMixin  {
  late BrandHomeNotifier brandHomeNotifier;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    brandHomeNotifier = Provider.of<BrandHomeNotifier>(context, listen: false);

    // 使用addPostFrameCallback延迟执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!brandHomeNotifier.initLoading &&
          brandHomeNotifier.tiktokBrandPageResponse == null) {
        brandHomeNotifier.startLoadingBrandCategory();
      }
    });
  }

  @override
  Future<void> onNetworkRestored() async {
    brandHomeNotifier.startLoadingBrandCategory();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // 触发刷新
          await brandHomeNotifier.startLoadingBrandCategory();
        },
        child: SingleChildScrollView(
          child: _buildContent(context),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          Assets.imagesBgBrandHomeTop,
          width: MediaQuery.of(context).size.width,
          fit: BoxFit.cover,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top + 60,
                left: 12,
                right: 12,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWithMarkup(
                    text: S.of(context).brand_home_top_title,
                    styles: {
                      "red": TextStyle(
                        color: highlightTextColor,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    },
                    defaultStyle: TextStyle(
                      color: primaryTextColor,
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12),
                  Text(
                    S.of(context).brand_home_top_subtitle,
                    style: TextStyle(
                      color: primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                child: Container(
                  padding: EdgeInsets.all(16),
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Text.rich(
                      //   TextSpan(
                      //     children: [
                      //       WidgetSpan(
                      //         child: Image.asset(
                      //           Assets.imagesIcBrandHomeTiktok20,
                      //         ),
                      //         alignment: PlaceholderAlignment.middle,
                      //       ),
                      //       TextSpan(text: '  '),
                      //       TextSpan(
                      //         text: S.of(context).brand_tiktok_hot_sale_title,
                      //         style: const TextStyle(
                      //           color: primaryTextColor,
                      //           fontSize: 16,
                      //           fontWeight: FontWeight.w600,
                      //         ),
                      //       ),
                      //       TextSpan(text: '  '),
                      //       WidgetSpan(
                      //         child: Image.asset(Assets.imagesIcBrandHomeStar),
                      //         alignment: PlaceholderAlignment.top,
                      //       ),
                      //     ],
                      //   ),
                      //   textAlign: TextAlign.start,
                      // ),
                      // SizedBox(height: 12),
                      // buildTiktokBrandView(context),
                      Text.rich(
                        TextSpan(
                          children: [
                            WidgetSpan(
                              child: Image.asset(
                                Assets.imagesIcHomeHighRebate,
                              ),
                              alignment: PlaceholderAlignment.middle,
                            ),
                            TextSpan(text: '  '),
                            TextSpan(
                              text: S.of(context).brand_high_rebate_title,
                              style: const TextStyle(
                                color: primaryTextColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            TextSpan(text: '  '),
                            WidgetSpan(
                              child: Image.asset(Assets.imagesIcBrandHomeStar),
                              alignment: PlaceholderAlignment.top,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.start,
                      ),
                      SizedBox(height: 12),
                      buildTopCashbackBrandsView(context)
                    ],
                  ),
                ),
              ),
            ),

          ],
        ),
      ],
    );
  }

  Widget buildTiktokBrandView(BuildContext context) {
    return Consumer<BrandHomeNotifier>(
      builder: (context, notifier, child) {
        if (notifier.initLoading) {
          return Center(child: CupertinoActivityIndicator());
        }
        if (notifier.tiktokBrandPageResponse == null) {
          return EmptyView();
        }
        return Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
          child: GridView.count(
            padding: EdgeInsets.zero,
            crossAxisCount: 3,
            mainAxisSpacing: 8,
            crossAxisSpacing: 8,
            childAspectRatio: 1,
            physics: NeverScrollableScrollPhysics(), // 禁止滚动
            shrinkWrap: true, // 紧凑模式
            children: notifier.tiktokBrandPageResponse?.list
                .map((model) => buildBrandItem(context, model))
                .toList() ?? [],
          )
        );
      },
    );
  }

  Widget buildTopCashbackBrandsView(BuildContext context) {
    return Consumer<BrandHomeNotifier>(
      builder: (context, notifier, child) {
        if (notifier.initLoading) {
          return Center(child: CupertinoActivityIndicator());
        }
        if (notifier.highRebaseBrandPageResponse == null) {
          return EmptyView();
        }
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          child: GridView.count(
            padding: EdgeInsets.zero,
            crossAxisCount: 3,
            mainAxisSpacing: 8,
            crossAxisSpacing: 8,
            childAspectRatio: 0.69,
            physics: NeverScrollableScrollPhysics(), // 禁止滚动
            shrinkWrap: true, // 紧凑模式
            children: notifier.highRebaseBrandPageResponse?.list
                .map((model) => buildBrandItem(context, model))
                .toList() ?? [],
          ),
        );
      },
    );
  }

  Widget buildBrandItem(BuildContext context,  ProductCategory model) {
    return InkWell(
      onTap: () {
        customRouter(context, BrandProductListScreen(category: model));
      },
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(12)),
              border: Border.all(color: backgroundColor, width: 1)
            ),
            child: ImageWidget(
              borderRadius: BorderRadius.all(Radius.circular(12)),
              url: model.logoUrl ?? "",
              fit: BoxFit.fitWidth,
              height: 100,
              width: 100,
              loadingWidth: 100,
              loadingHeight: 100,
              key: Key("${model.id}"),
            ),
          ),
          SizedBox(height: 6),
          Text(
            model.name,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          // SizedBox(height: 6),
          // Text(
          //   "${model.maxCashBackRate ?? "0"}%",
          //   style: TextStyle(
          //     color: highlightTextColor,
          //     fontSize: 12,
          //     fontWeight: FontWeight.w400,
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildSkeletonItem(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 图片占位 (圆角效果)
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: SkeletonWidget(
              height: 50,
              width: double.infinity, // 宽度填满父容器
            ),
          ),

          SizedBox(height: 6),

          // "品牌最高返利率" 文本占位
          SkeletonWidget(
            height: 10,
            width: 100, // 模拟文本宽度
          ),

          SizedBox(height: 6),

          // 百分比数值占位
          SkeletonWidget(
            height: 12,
            width: 40, // 模拟百分比文本宽度
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Future<void> homeTabDoubleTapRefresh()async {
    Logger().d("homeTabDoubleTapRefresh");
    brandHomeNotifier.startLoadingBrandCategory();
  }
}
