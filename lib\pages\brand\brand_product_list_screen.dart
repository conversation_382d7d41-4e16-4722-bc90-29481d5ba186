import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/brand_product_filter.dart';
import 'package:milestone/models/home_platform.dart';
import 'package:milestone/pages/home/<USER>';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/image_indicator.dart';
import 'package:share_plus/share_plus.dart';

import '../../generated/assets.dart';
import '../../models/category.dart';
import '../../widget/image_widget.dart';
import 'category_single_page.dart';

class BrandProductListScreen extends StatefulWidget {
  final ProductCategory category;

  const BrandProductListScreen({super.key, required this.category});

  @override
  State<StatefulWidget> createState() => _BrandProductListScreenState();
}

class _BrandProductListScreenState extends State<BrandProductListScreen> {
  int productCount = 0;

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      appBar: buildAppBarButtons(context),
        backgroundImage: R.assetsImagesBgBrandProductList,
        backgroundColor: Colors.white,
        title: "",
        child: DefaultTabController(
          length: brandProductFilters.length,
          child: Scaffold(
            backgroundColor: Colors.transparent,
            body: ExtendedNestedScrollView(
              onlyOneScrollInBody: true,
              physics: const BouncingScrollPhysics(),
              headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
                return headerSilverBuilder(context, innerBoxIsScrolled);
              },
              body: Container(
                color: Colors.white,
                child: builtTabBarView(context),
              ),
            ),
          ),
        )
    );
  }

  List<Widget> headerSilverBuilder(
    BuildContext context,
    bool innerBoxIsScrolled,
  ) {
    return <Widget>[
      buildTopHeader(context),
      buildAppbar(context)
    ];
  }

  Widget buildTopHeader(BuildContext context) {
    return SliverToBoxAdapter(
      child: Stack(
        children: [
          Column(
            children: [
              buildBrandInformation(context),
              SizedBox(height: 20),
              Container(
                width: MediaQuery.of(context).size.width,
                height: 15,
                padding: EdgeInsets.zero,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(15),
                    topRight: Radius.circular(15),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildBrandInformation(BuildContext context) {
    double imageWidth = 60;
    return Container(
      padding: EdgeInsets.only(left: 16, right: 16, top: 16),
      child: Row(
        children: [
          ClipOval(
            child: ImageWidget(
              width: imageWidth,
              height: imageWidth,
              url: widget.category.logoUrl,
              defaultImagePath: Assets.imagesIcAvatarDefault,
              loadingWidth: imageWidth,
              loadingHeight: imageWidth,
            ),
          ),
          SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.category.name,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: primaryTextColor,
                ),
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  Image.asset(R.assetsImagesIcBrandGoodAmount),
                  SizedBox(width: 4),
                  Text(
                    "${S.of(context).detail_brand_product_amount_pre} $productCount ${S.of(context).detail_brand_product_amount}",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: unSelectedTextColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  AppBar buildAppBarButtons(BuildContext context) {
    return AppBar(
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle.dark,
      backgroundColor: Colors.transparent,
      leading: IconButton(
        color: Theme.of(context).colorScheme.onSecondary,
        icon: Image.asset(R.assetsImagesIcDetailBackArrow),
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
      // actions: [
      //   IconButton(
      //     onPressed: () async {
      //       String shareText = widget.category.name;
      //       await SharePlus.instance.share(
      //           ShareParams(
      //               text: shareText
      //           )
      //       );
      //     },
      //     icon: Container(
      //       width: 29,
      //       height: 29,
      //       alignment: Alignment.center,
      //       decoration: BoxDecoration(
      //         color: Colors.black.withValues(alpha: 0.3),
      //         borderRadius: BorderRadius.circular(14),
      //       ),
      //       child: Image.asset(R.assetsImagesIcDetailShare),
      //     ),
      //   ),
      // ],
    );
  }

  Widget buildAppbar(BuildContext context) {
    return SliverAppBar(
      backgroundColor: Colors.white,
      centerTitle: true,
      floating: true,
      pinned: false,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      toolbarHeight: 40,
      flexibleSpace: Align(
        alignment: Alignment.centerLeft,
        child: TabBar(
          isScrollable: true,
          dividerColor: Colors.transparent,
          indicatorSize: TabBarIndicatorSize.tab,
          tabAlignment: TabAlignment.start,
          labelColor: primaryTextColor,
          labelStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
          unselectedLabelColor: unSelectedTextColor,
          unselectedLabelStyle: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w400,
          ),
          indicator: ImageIndicator(),
          tabs: brandProductFilters.map((e) => e.tabView(context)).toList(),
        ),
      ),
    );
  }

  Widget builtTabBarView(BuildContext context) {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      children: brandProductFilters.map((e) {
        return CategorySinglePage(
          code: widget.category.code,
          brandProductFilter: e,
          onProductCountChanged: (count) {
            setState(() {
              productCount = count;
            });
          },
        );
      }).toList(),
    );
  }
}
