import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/models/product_list.dart';
import 'package:milestone/pages/detail/product_detail_screen.dart';
import 'package:milestone/pages/home/<USER>';
import 'package:milestone/pages/home/<USER>';
import 'package:milestone/widget/loading_error_view.dart';
import 'package:milestone/widget/loading_more_indicator.dart';

import '../../models/brand_product_filter.dart';
import '../../utils/navigation_route.dart';
import 'category_single_page_loading_more.dart';

class CategorySinglePage extends StatefulWidget {
  final Widget? headerContent;
  final int columnCount;
  final BrandProductFilter brandProductFilter;
  final String code;
  final Function(int) onProductCountChanged;

  const CategorySinglePage({
    super.key,
    this.headerContent,
    this.columnCount = 2,
    required this.brandProductFilter,
    required this.onProductCountChanged, required this.code
  });

  @override
  State<StatefulWidget> createState() => CategorySinglePageState();
}

class CategorySinglePageState extends State<CategorySinglePage>
    with AutomaticKeepAliveClientMixin {
  late ScrollController scrollController = ScrollController();
  late CategorySinglePageLoadingMoreAdapter loadingMoreController;
  late HomeSinglePageNotifier pageNotifier;
  bool isLoadingMore = true;
  bool isRequesting = false;
  bool isScrolling = false;
  bool isOffstageTop = true;
  double padding = 8;
  int columnCount = 2;
  Future? data;
  int page = 0;

  @override
  void initState() {
    loadingMoreController = CategorySinglePageLoadingMoreAdapter(widget.brandProductFilter, widget.code);
    if (loadingMoreController.isEmpty) {
      data = startLoading(true);
    }
    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    loadingMoreController.dispose();
    super.dispose();
  }

  Future startLoading(bool refresh) async {
    if (isRequesting) {
      return;
    }
    setState(() {
      isRequesting = true;
    });
    await loadingMoreController.refresh(refresh);
    widget.onProductCountChanged(loadingMoreController.length);
    setState(() {
      isLoadingMore = true;
      isRequesting = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return FutureBuilder(builder: buildHomeContent, future: data);
  }

  Widget buildHomeContent(BuildContext context, AsyncSnapshot snapshot) {
    switch (snapshot.connectionState) {
      case ConnectionState.none:
      case ConnectionState.waiting:
        return const Center(child: CupertinoActivityIndicator());
      case ConnectionState.active:
      case ConnectionState.done:
        if (snapshot.hasError) {
          return LoadingErrorView(
            error: "${snapshot.error}",
            onRetry: () {
              data = startLoading(true);
            },
          );
        }
        if (loadingMoreController.isEmpty && isRequesting) {
          return const Center(child: CupertinoActivityIndicator());
        } else {
          return buildContent(context);
        }
    }
  }

  Widget buildContent(BuildContext context) {
    double padding = 8;
    int columnCount = widget.columnCount;

    return MediaQuery.removePadding(
      removeTop: true,
      context: context,
      child: Stack(
        children: [
          //renderHeaderContent(),
          Container(
            margin: EdgeInsets.only(left: padding, right: padding),
            child: LoadingMoreCustomScrollView(
              slivers: [
                //renderHeaderContent(),
                LoadingMoreSliverList(
                  SliverListConfig<Product>(
                    extendedListDelegate:
                        SliverWaterfallFlowDelegateWithFixedCrossAxisCount(
                          crossAxisCount: columnCount,
                          crossAxisSpacing: padding,
                          mainAxisSpacing: padding,
                          lastChildLayoutTypeBuilder: (index) =>
                              index == loadingMoreController.length
                              ? LastChildLayoutType.foot
                              : LastChildLayoutType.none,
                        ),
                    itemBuilder: (context, entity, index) {
                      return GestureDetector(
                        onTap: () {
                          customRouter(context, ProductDetailScreen(productId: entity.id));
                        },
                        child: HomeSingleProduct(
                          product: entity,
                          columnCount: columnCount,
                          key: Key("${entity.id}"),
                        ),
                      );
                    },
                    sourceList: loadingMoreController,
                    indicatorBuilder: (context, state) {
                      return LoadingMoreIndicator(
                        state,
                        tryAgainFunction: () {
                          data = startLoading(true);
                        },
                        emptyLinkFunction: () {
                          data = startLoading(true);
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          buildOffstageButton(),
        ],
      ),
    );
  }

  Widget buildOffstageButton() {
    return Positioned(
      bottom: 50,
      right: 20,
      child: Offstage(
        offstage: isOffstageTop,
        child: GestureDetector(
          onTap: () {
            scrollController.animateTo(
              0,
              duration: const Duration(seconds: 1),
              curve: Curves.ease,
            );
          },
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.arrow_upward, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget renderHeaderContent() {
    if (widget.headerContent != null) {
      return SliverToBoxAdapter(child: widget.headerContent);
    } else {
      return SliverToBoxAdapter(child: Container());
    }
  }

  @override
  bool get wantKeepAlive => true;
}
