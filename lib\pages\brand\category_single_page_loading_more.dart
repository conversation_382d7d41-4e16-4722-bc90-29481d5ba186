import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/models/product_list.dart';

import '../../models/brand_product_filter.dart';
import '../../network/network_api_client.dart';


class CategorySinglePageLoadingMoreAdapter extends LoadingMoreBase<Product> {
  final BrandProductFilter brandProductFilter;
  final String code;
  bool isAsc = true;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  CategorySinglePageLoadingMoreAdapter(this.brandProductFilter, this.code);

  @override
  bool get hasMore => _hasMore;

  @override
  Future<bool> loadData([bool isLoadMoreAction = false]) async {
    if (!isLoadMoreAction) {
      clear();
      _page = 0;
    }
    ProductListResponse productListResponse = await networkApiClient.getBrandCategoryProducts(code, brandProductFilter, isAsc);
    addAll(productListResponse.list);
    _page++;
    _hasMore = productListResponse.page < productListResponse.totalPage;

    return true;
  }
}