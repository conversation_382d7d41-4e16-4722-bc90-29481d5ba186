import 'package:flutter/material.dart';
import 'package:milestone/themes/colors.dart';

import '../../generated/l10n.dart';
import '../../r.dart';
import '../../widget/dash_border/dotted_border.dart';
import '../../widget/dash_border/dotted_border_options.dart';

class OrderingView extends StatelessWidget {
  final String amount;
  final VoidCallback? onConfirm;
  const OrderingView({super.key, required this.amount, this.onConfirm});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          R.assetsImagesBgOrder,
          fit: BoxFit.cover,
          width: MediaQuery.of(context).size.width,
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 25.0, horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(R.assetsImagesLogo48),
                  SizedBox(width: 8),
                  Image.asset(R.assetsImagesIcOrderPoint),
                  SizedBox(width: 8),
                  Image.asset(R.assetsImagesTiktok48),
                ],
              ),
              SizedBox(height: 12),
              Text(
                S.of(context).jump_to_tiktok,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
              SizedBox(height: 12),
              DottedBorder(
                options: RoundedRectDottedBorderOptions(
                  dashPattern: [10, 5],
                  strokeWidth: 1,
                  radius: Radius.circular(6),
                  color: secondaryTextColor,
                  padding: EdgeInsets.all(4),
                  borderPadding: EdgeInsets.symmetric(
                    vertical: 0,
                    horizontal: 22,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      S.of(context).detail_cashback_amount,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(width: 8),
                        Text(
                          "Rp",
                          style: TextStyle(
                            color: highlightTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        Text(
                          amount,
                          style: TextStyle(
                            color: highlightTextColor,
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12),
              InkWell(
                onTap: () async {
                  onConfirm?.call();
                },
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 22),
                  width: MediaQuery.of(context).size.width,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Color(0xFFFFE9C7),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Center(
                    child: Text(
                      S.of(context).button_next,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

void showOrderingDialog({
  required BuildContext context,
  required String amount,
  Duration animationDuration = const Duration(milliseconds: 300),
  Curve animationCurve = Curves.easeOutBack,
  VoidCallback? onConfirm,
}) {
  showGeneralDialog(
    context: context,
    barrierDismissible: false,
    barrierColor: Colors.black.withValues(alpha: 0.4),
    transitionDuration: animationDuration,
    pageBuilder:
        (
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
        ) {
          return Material(
            color: Colors.transparent,
            child: Center(
              child: ScaleTransition(
                scale: CurvedAnimation(
                  parent: animation,
                  curve: Interval(0.0, 1.0, curve: animationCurve),
                ),
                child: FadeTransition(
                  opacity: CurvedAnimation(
                    parent: animation,
                    curve: Interval(0.0, 1.0, curve: Curves.easeOut),
                  ),
                  child: _buildDialogContent(context, amount, onConfirm),
                ),
              ),
            ),
          );
        },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return ScaleTransition(
        scale: CurvedAnimation(
          parent: animation,
          curve: animationCurve,
        ).drive(Tween(begin: 0.8, end: 1.0)),
        child: FadeTransition(
          opacity: CurvedAnimation(parent: animation, curve: Curves.easeOut),
          child: child,
        ),
      );
    },
  );
}

Widget _buildDialogContent(
  BuildContext context,
  String amount,
  VoidCallback? onConfirm,
) {
  return Dialog(
    backgroundColor: Colors.transparent,
    elevation: 0,
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        OrderingView(amount: amount, onConfirm: onConfirm),
        SizedBox(height: 20),
        InkWell(
          onTap: () {
            Navigator.of(context).pop();
          },
          child: Container(
            width: 35,
            height: 35,
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.white, width: 1),
            ),
            child: Center(child: Image.asset(R.assetsImagesIcCloseWhite)),
          ),
        ),
      ],
    ),
  );
}
