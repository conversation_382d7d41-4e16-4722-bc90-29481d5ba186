import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/models/product_list.dart';
import 'package:milestone/pages/detail/product_detail_notifier.dart';

class ProductDetailLoadingMoreAdapter extends LoadingMoreBase<Product> {
  final ProductDetailNotifier notifier;

  ProductDetailLoadingMoreAdapter(this.notifier) {
    notifier.addListener(_updateState);
  }

  void _updateState() {
    if (notifier.hasError) {
      indicatorStatus = notifier.products.isEmpty
          ? IndicatorStatus.fullScreenError
          : IndicatorStatus.error;
    }
    if (notifier.products.length != length) {
      refresh(true);
    }
  }

  @override
  void dispose() {
    notifier.removeListener(_updateState);
    super.dispose();
  }

  @override
  bool get hasMore => notifier.hasMore;

  @override
  Future<bool> refresh([bool notifyStateChanged = false]) async {
    clear();
    return super.refresh(notifyStateChanged);
  }

  @override
  Future<bool> loadData([bool isLoadMoreAction = false]) async {
    try {
      if (isLoadMoreAction) {
        await notifier.loadMoreData();
      } else {
        await notifier.loadInitialData();
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  int get length => notifier.products.length;

  @override
  Product operator [](int index) {
    return notifier.products[index];
  }
}