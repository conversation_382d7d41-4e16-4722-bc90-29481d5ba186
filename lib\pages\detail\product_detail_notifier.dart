import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:milestone/models/home_platform.dart';
import 'package:milestone/models/product_info.dart';
import 'package:milestone/models/product_list.dart';
import 'package:milestone/network/network_api_client.dart';

class ProductDetailNotifier extends ChangeNotifier {
  int productId = 0;
  ProductListResponse? productListResponse;
  List<Product> get products => productListResponse?.list ?? [];
  ProductResponse? productResponse;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  bool _isLoadingMore = false;
  bool get isLoadingMore => _isLoadingMore;

  bool _hasError = false;
  bool get hasError => _hasError;

  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = false;
  bool get hasMore => _hasMore;

  int carouselPage = 0;

  void updateCarouselPage(int page) {
    carouselPage = page;
    notifyListeners();
  }

  Future<bool> loadInitialData() async {
    if (_isLoading) return false;

    try {
      _isLoading = true;
      _hasError = false;
      notifyListeners();

      await _loadPageData();
      if (productListResponse != null) {
        _page = productListResponse!.page;
        _hasMore = productListResponse!.list.length >= productListResponse!.limit;
      } else {
        _page = 0;
        _hasMore = false;
      }

      return true;
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadMoreData() async {
    if (!_hasMore || _isLoadingMore || _isLoading) return;

    try {
      _isLoadingMore = true;
      notifyListeners();
      await _loadPageData();
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  Future<void> refreshData() async {
    loadInitialData();
  }

  bool loadingDetail = false;

  Future<void> loadProductDetail(int id) async {
    productId = id;
    loadingDetail = true;
    notifyListeners();
    productResponse = await networkApiClient.getProductDetail(productId);
    Logger().d("productResponse:$productResponse");
    notifyListeners();
  }

  Future<void> _loadPageData() async {
    try {
      productListResponse = await networkApiClient.getRecommendProducts(productId, page: _page, limit: _pageSize);
    } catch  (e) {
      Logger().d(e.toString());
    }
  }

  Future<void> addToCollection(int id) async {
    networkApiClient.addProductToFavorites(id, "store");
  }
}
