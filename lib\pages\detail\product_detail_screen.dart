import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/product_list.dart';
import 'package:milestone/pages/brand/brand_product_list_screen.dart';
import 'package:milestone/pages/detail/product_brand_information_view.dart';
import 'package:milestone/pages/detail/product_detail_loading_more.dart';
import 'package:milestone/pages/detail/product_detail_notifier.dart';
import 'package:milestone/pages/home/<USER>';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/favorite_manager.dart';
import 'package:milestone/widget/image_widget.dart';
import 'package:milestone/widget/loading_dialog.dart';
import 'package:milestone/widget/loading_more_indicator.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:milestone/utils/string.dart';

import '../../models/product_info.dart';
import '../../models/share_link_product_info.dart';
import '../../network/errors.dart';
import '../../network/network_api_client.dart';
import '../../utils/navigation_route.dart';
import '../../utils/tiktok_login.dart';
import '../../utils/toast_utils.dart';
import '../../widget/loading_error_view.dart';
import '../guidelines/usage_guidelines_screen.dart';
import 'detail_single_product.dart';
import 'ordering_view.dart';

class ProductDetailScreen extends StatefulWidget {
  final int productId;
  final ProductResponse? productResponse;
  final ShareLinkProductInfo? shareLinkProductInfo;
  const ProductDetailScreen({
    super.key,
    required this.productId,
    this.productResponse,
    this.shareLinkProductInfo,
  });

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen> {
  late ProductDetailLoadingMoreAdapter loadingMoreController;
  late ProductDetailNotifier productDetailNotifier;
  ProductResponse? productResponse;
  bool _hasTriggeredOrdering = false;
  double padding = 8;
  int columnCount = 2;
  bool _initialized = false;
  bool isFavorite = false;
  Future? data;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      productDetailNotifier = Provider.of<ProductDetailNotifier>(
        context,
        listen: false,
      );
      productDetailNotifier.productId = widget.productId;
      loadingMoreController = ProductDetailLoadingMoreAdapter(
        productDetailNotifier,
      );
      loadingMoreController.loadData(false);
      _initialized = true;
    }
  }

  @override
  void initState() {
    if (Platform.isAndroid) {
      SystemUiOverlayStyle style = const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      );
      SystemChrome.setSystemUIOverlayStyle(style);
    }
    Logger().d("product: ${widget.productId}");
    isFavorite = FavoriteManager().isFavorite(widget.productId);
    super.initState();
    if (widget.productResponse == null) {
      data = startLoading();
    } else {
      productResponse = widget.productResponse;
    }

    if (widget.shareLinkProductInfo != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_hasTriggeredOrdering) {
          _hasTriggeredOrdering = true;
          startOrdering(context);
        }
      });
    }
  }

  @override
  void dispose() {
    loadingMoreController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.productResponse == null) {
      return FutureBuilder(builder: buildHomeContent, future: data);
    } else {
      return buildContent(context);
    }
  }

  Future startLoading() async {
    productResponse = await networkApiClient.getProductDetail(widget.productId);
    Logger().d("productResponse:$productResponse");
  }

  Widget buildHomeContent(BuildContext context, AsyncSnapshot snapshot) {
    switch (snapshot.connectionState) {
      case ConnectionState.none:
      case ConnectionState.waiting:
        return Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          color: Colors.white,
          child: Center(child: CupertinoActivityIndicator()),
        );
      case ConnectionState.active:
      case ConnectionState.done:
        if (snapshot.hasError) {
          return LoadingErrorView(
            error: "${snapshot.error}",
            onRetry: () {
              productDetailNotifier.loadProductDetail(widget.productId);
            },
          );
        } else {
          return buildContent(context);
        }
    }
  }

  Widget buildContent(BuildContext context) {
    return MediaQuery.removePadding(
      removeTop: true,
      context: context,
      child: Stack(
        children: [
          Container(
            color: backgroundColor,
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
          ),
          LoadingMoreCustomScrollView(
            slivers: [
              buildHeaderContent(context),
              buildSliverWaterFlowView(context),
              SliverToBoxAdapter(
                child: SizedBox(
                  height: MediaQuery.of(context).padding.bottom + 80,
                ),
              ),
            ],
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: buildBottomOperationBar(context),
          ),
        ],
      ),
    );
  }

  Widget buildBottomOperationBar(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).padding.bottom + 80,
      padding: EdgeInsets.only(
        left: 12,
        right: 12,
        top: 12,
        bottom: 12 + MediaQuery.of(context).padding.bottom,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(12),
          bottom: Radius.circular(0),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          InkWell(
            onTap: () async {
              try {
                showLoadingDialog();
                if (FavoriteManager().isFavorite(widget.productId)) {
                  await networkApiClient.removeProductFromFavorites(
                    widget.productId,
                  );
                  FavoriteManager().toggleFavorite(widget.productId);
                  setState(() {
                    isFavorite = false;
                  });
                  dismissLoadingDialog();
                } else {
                  await networkApiClient.addProductToFavorites(
                    widget.productId,
                    "store",
                  );
                  FavoriteManager().toggleFavorite(widget.productId);
                  setState(() {
                    isFavorite = true;
                  });
                  dismissLoadingDialog();
                }
              } catch (e) {
                dismissLoadingDialog();
                if (context.mounted) {
                  ApiErrorHandler.handleError(e, context);
                }
              }
            },
            child: SizedBox(
              width: 80,
              height: 40,
              child: Column(
                children: [
                  Image.asset(
                    isFavorite
                        ? R.assetsImagesIcFavriteStarFill
                        : R.assetsImagesIcFavriteStar,
                  ),
                  SizedBox(height: 4),
                  Text(
                    S.of(context).collection,
                    style: const TextStyle(
                      color: primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: InkWell(
              onTap: () {
                startOrdering(context);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                height: 45,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFFC80D1F), Color(0xFFFB4143)],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Column(
                    children: [
                      Text(
                        S.of(context).order_right_now,
                        style: TextStyle(
                          color: Color(0xFFFFE9C7),
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 0),
                      Text(
                        "${S.of(context).order_expected_cashback} Rp${(productResponse?.productInfo?.cashBackAmount as String).formatIDR() ?? "0"}",
                        style: TextStyle(
                          color: Color(0xFFFFE9C7),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void startOrdering(BuildContext context) {
    showOrderingDialog(
      context: context,
      amount: productResponse?.productInfo?.cashBackAmount ?? "",
      onConfirm: () async {
        try {
          showLoadingDialog();
          ShareLinkProductInfo productInfo = await networkApiClient
              .getProductShareInfo(widget.productId);
          dismissLoadingDialog();
          if (productInfo.shareLink.isNotEmpty) {
            final Uri uri = Uri.parse(productInfo.shareLink);
            if (!await launchUrl(uri, mode: LaunchMode.platformDefault) &&
                context.mounted) {
              makeToast(S.of(context).can_not_open_link);
            }
          }
          if (context.mounted) {
            Navigator.of(context).pop();
          }
        } catch (e) {
          if (context.mounted) {
            dismissLoadingDialog();
            ApiErrorHandler.handleError(e, context);
          }
        }
      },
    );
  }

  Widget buildSliverWaterFlowView(BuildContext context) {
    return LoadingMoreSliverList(
      SliverListConfig<Product>(
        extendedListDelegate:
            SliverWaterfallFlowDelegateWithFixedCrossAxisCount(
              crossAxisCount: columnCount,
              crossAxisSpacing: padding,
              mainAxisSpacing: padding,
              lastChildLayoutTypeBuilder: (index) =>
                  index == loadingMoreController.length
                  ? LastChildLayoutType.foot
                  : LastChildLayoutType.none,
            ),
        itemBuilder: (context, entity, index) {
          return GestureDetector(
            onTap: () {
              customRouter(context, ProductDetailScreen(productId: entity.id));
            },
            child: HomeSingleProduct(
              product: entity,
              columnCount: columnCount,
              key: Key("${entity.id}"),
            ),
          );
        },
        sourceList: loadingMoreController,
        indicatorBuilder: (context, state) {
          return LoadingMoreIndicator(
            state,
            tryAgainFunction: () {
              loadingMoreController.loadData(false);
            },
            emptyLinkFunction: () {
              loadingMoreController.loadData(false);
            },
          );
        },
      ),
    );
  }

  Widget buildHeaderContent(BuildContext context) {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          Stack(
            children: [
              buildProductCoverCarousel(context),
              buildAppBar(context),
              buildIndicator(context),
            ],
          ),
          buildProductInformationView(context),
          InkWell(
            onTap: () {
              customRouter(context, UsageGuidelinesScreen());
            },
            child: buildCashbackFlowView(context),
          ),
          buildBrandIntroductionView(context),
        ],
      ),
    );
  }

  Widget buildProductInformationView(BuildContext context) {
    double cashbackRate =
        double.parse(productResponse?.productInfo?.cashBackRate ?? "0") * 100;

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
          topLeft: Radius.zero,
          topRight: Radius.zero,
        ),
        color: Colors.white,
      ),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text.rich(
                  TextSpan(
                    children: [
                      WidgetSpan(
                        child: Image.asset(R.assetsImagesIcHomeTiktok14),
                        alignment: PlaceholderAlignment.middle,
                      ),
                      TextSpan(text: ' '),
                      TextSpan(
                        text: productResponse?.productInfo?.storeName,
                        style: const TextStyle(
                          color: primaryTextColor,
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Text(
                S.of(context).detail_price_title,
                style: const TextStyle(
                  color: unSelectedTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Text(
                "Rp${(productResponse?.productInfo?.salesPrice as String ).formatIDR() ?? "0"}",
                style: const TextStyle(
                  color: highlightTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Spacer(),
              Text(
                "${S.of(context).detail_sold} ${productResponse?.productInfo?.sales ?? 0} ${S.of(context).detail_sold_count}",
                style: const TextStyle(
                  color: unSelectedTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Stack(
            children: [
              Image.asset(
                R.assetsImagesBgDetailCashback,
                width: MediaQuery.of(context).size.width - 12 * 2,
                fit: BoxFit.cover,
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: "${S.of(context).detail_cashback_amount} Rp",
                            style: const TextStyle(
                              color: highlightTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          TextSpan(
                            text:
                                (productResponse?.productInfo?.cashBackAmount
                                        as String)
                                    .formatIDR() ??
                                "0",
                            style: const TextStyle(
                              color: highlightTextColor,
                              fontSize: 20,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          TextSpan(text: " "),
                          WidgetSpan(
                            child: Image.asset(R.assetsImagesIcDetailQuestion),
                            alignment: PlaceholderAlignment.middle,
                          ),
                        ],
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),

                    Text(
                      "${S.of(context).detail_rebate_rate} ${cashbackRate.toStringAsFixed(2)}%",
                      style: const TextStyle(
                        color: unSelectedTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildArrowDivider() {
    return Container(
      alignment: Alignment.center, // 确保垂直居中
      padding: const EdgeInsets.only(bottom: 24), // 视觉微调
      height: 70,
      child: Image.asset(R.assetsImagesIcDetailCashbackFlowPoint),
    );
  }

  Widget buildCashbackFlowView(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 6, top: 6),
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(12)),
        color: Colors.white,
      ),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                S.of(context).detail_cashback_flow_title,
                style: const TextStyle(
                  color: primaryTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Spacer(),
              Text(
                S.of(context).detail_cashback_flow_check,
                style: const TextStyle(
                  color: unSelectedTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Image.asset(R.assetsImagesIcDetailArrowRight),
            ],
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  Image.asset(R.assetsImagesIcDetailCashbackFlow1),
                  Text(
                    S.of(context).detail_cashback_flow_step1,
                    style: const TextStyle(
                      color: thirdTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              _buildArrowDivider(),
              Column(
                children: [
                  Image.asset(R.assetsImagesIcDetailCashbackFlow2Tiktok),
                  Text(
                    S.of(context).detail_cashback_flow_step2,
                    style: const TextStyle(
                      color: thirdTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              _buildArrowDivider(),
              Column(
                children: [
                  Image.asset(R.assetsImagesIcDetailCashbackFlow3),
                  Text(
                    S.of(context).detail_cashback_flow_step3,
                    style: const TextStyle(
                      color: thirdTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              _buildArrowDivider(),
              Column(
                children: [
                  Image.asset(R.assetsImagesIcDetailCashbackFlow4),
                  Text(
                    S.of(context).detail_cashback_flow_step4,
                    style: const TextStyle(
                      color: thirdTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildBrandIntroductionView(BuildContext context) {
    if (productResponse?.storeBrand != null) {
      return ProductBrandInformation(category: productResponse!.storeBrand!);
    } else {
      return Container();
    }
  }

  Widget buildProductCoverCarousel(BuildContext context) {
    return CarouselSlider(
      options: CarouselOptions(
        height: MediaQuery.of(context).size.width,
        autoPlay: false,
        viewportFraction: 1,
        enlargeCenterPage: false,
        autoPlayInterval: const Duration(seconds: 2),
        onPageChanged: (index, reason) {
          productDetailNotifier.updateCarouselPage(index);
        },
      ),
      items: [productResponse?.productInfo?.image ?? ""].map((imageUrl) {
        return buildSingleCoverImage(imageUrl);
      }).toList(),
    );
  }

  Widget buildIndicator(BuildContext context) {
    return Consumer<ProductDetailNotifier>(
      builder: (context, store, child) {
        return Positioned(
          bottom: 12,
          right: 12,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${store.carouselPage + 1} / ${[productResponse?.productInfo?.image ?? ""].length}',
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
        );
      },
    );
  }

  Widget buildSingleCoverImage(String imageUrl) {
    return ImageWidget(
      borderRadius: BorderRadius.zero,
      url: imageUrl,
      fit: BoxFit.cover,
      width: MediaQuery.of(context).size.width,
    );
  }

  Widget buildAppBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
      height: 120 + MediaQuery.of(context).padding.top,
      child: AppBar(
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          color: Theme.of(context).colorScheme.onSecondary,
          icon: Image.asset(R.assetsImagesIcDetailBackArrow),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        actions: [
          IconButton(
            onPressed: () async {
              try {
                showLoadingDialog();
                ShareLinkProductInfo productInfo = await networkApiClient
                    .getProductShareInfo(widget.productId);
                if (productInfo.shareLink.isNotEmpty && context.mounted) {
                  dismissLoadingDialog();
                  String amount = productResponse?.productInfo?.price ?? "";
                  String shareText = S
                      .of(context)
                      .share_text(amount + productInfo.shareLink);
                  await SharePlus.instance.share(ShareParams(text: shareText));
                }
              } catch (e) {
                if (context.mounted) {
                  dismissLoadingDialog();
                  ApiErrorHandler.handleError(e, context);
                }
              }
            },
            icon: Container(
              width: 29,
              height: 29,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(14),
              ),
              child: Image.asset(R.assetsImagesIcDetailShare),
            ),
          ),
        ],
      ),
    );
  }
}
