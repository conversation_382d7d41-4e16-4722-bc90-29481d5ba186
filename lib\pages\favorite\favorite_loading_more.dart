import 'dart:async';
import 'dart:math';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/models/order_item.dart';
import 'package:milestone/models/order_status.dart';

import '../../models/collected_product_item.dart';
import '../../network/network_api_client.dart';

class FavoriteLoadingMoreAdapter extends LoadingMoreBase<CollectedProductItem> {
  int _page = 1;
  final int _pageSize = 10;
  bool _hasMore = true;

  @override
  bool get hasMore => _hasMore;

  @override
  Future<bool> loadData([bool isLoadMoreAction = false]) async {
    await Future.delayed(Duration(seconds: 1)); // 模拟网络延迟

    if (!isLoadMoreAction) {
      _page = 1;
      clear();
    }

    final result = await networkApiClient.getCollectedProductList();

    if (result.list.isEmpty || result.page >= result.totalPage) {
      _hasMore = false;
    } else {
      _page++;
    }
    addAll(result.list);
    return true;
  }
}
