import 'package:flutter/material.dart';
import 'package:milestone/models/invitee_info.dart';

import '../../generated/assets.dart';
import '../../generated/l10n.dart';
import '../../models/user_level.dart';
import '../../themes/colors.dart';
import '../../widget/image_widget.dart';

class GroupInviteItemView extends StatelessWidget {
  final InviteeInfo inviteeInfo;
  const GroupInviteItemView({super.key, required this.inviteeInfo});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ClipOval(
                child: ImageWidget(
                  width: 52,
                  height: 52,
                  url: inviteeInfo.avatar,
                  defaultImagePath: Assets.imagesIcAvatarDefault,
                  loadingWidth: 52,
                  loadingHeight: 52,
                ),
              ),
              <PERSON><PERSON><PERSON><PERSON>(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        inviteeInfo.nickname,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: primaryTextColor,
                        ),
                      ),
                      Image.asset(memberLevelBadge(inviteeInfo.level ?? 0)),
                      Text(
                        memberLevelPublicName(
                          context,
                          inviteeInfo.level ?? 0,
                        ),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  Text(
                    "${S.of(context).invite_time}: ${inviteeInfo.time}",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: secondaryTextColor,
                    ),
                  ),
                ],
              ),
              Spacer(),
              Image.asset(Assets.imagesIcCallPhone),
            ],
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              color: Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            "Rp",
                            style: TextStyle(
                              color: primaryTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          Text(
                            "0",
                            style: TextStyle(
                              color: primaryTextColor,
                              fontSize: 17,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        S.of(context).pre_team_cashback,
                        style: TextStyle(
                          color: Color(0xFF98938F),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                VerticalDivider(
                  color: Colors.grey,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            "Rp",
                            style: TextStyle(
                              color: primaryTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          Text(
                            "0",
                            style: TextStyle(
                              color: primaryTextColor,
                              fontSize: 17,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        S.of(context).received_bonus,
                        style: TextStyle(
                          color: Color(0xFF98938F),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
