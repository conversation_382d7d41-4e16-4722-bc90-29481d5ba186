import 'package:flutter/material.dart';
import 'package:loading_more_list/loading_more_list.dart';

import '../../generated/l10n.dart';
import '../../models/invitee_info.dart';
import '../../models/user_level.dart';
import '../../widget/empty_view.dart';
import '../../widget/loading_more_indicator.dart';
import 'group_invite_item_view.dart';
import 'invitee_list_source.dart';

class GroupInviteeListView extends StatefulWidget {
  final AgentFilters agentFilters;
  final String? sortKey;
  final String? isAsc;
  const GroupInviteeListView({
    super.key,
    required this.agentFilters,
    this.sortKey,
    this.isAsc,
  });

  @override
  State<GroupInviteeListView> createState() => _GroupInviteeListViewState();
}

class _GroupInviteeListViewState extends State<GroupInviteeListView> {
  late InviteeListSource source;

  @override
  void initState() {
    super.initState();
    source = InviteeListSource(
      memberLevelFilter: widget.agentFilters,
      sortKey: widget.sortKey,
      isAsc: widget.isAsc,
    );
  }

  @override
  void didUpdateWidget(GroupInviteeListView oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.sortKey != widget.sortKey ||
        oldWidget.isAsc != widget.isAsc) {
      source.sortKey = widget.sortKey;
      source.isAsc = widget.isAsc;
      source.loadData();
    }
  }

  @override
  void dispose() {
    source.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LoadingMoreList<InviteeInfo>(
      ListConfig<InviteeInfo>(
        itemBuilder: (context, item, index) => _buildInviteeItem(item),
        sourceList: source,
        indicatorBuilder: (context, status) {
          switch (status) {
            case IndicatorStatus.none:
            case IndicatorStatus.loadingMoreBusying:
            case IndicatorStatus.fullScreenBusying:
              return buildLoadingMoreContent(context);
            case IndicatorStatus.noMoreLoad:
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Text(
                    S.of(context).loading_more_no_more,
                    style: const TextStyle(
                      color: Color(0xFFCCCCCC),
                      fontSize: 14,
                    ),
                  ),
                ),
              );
            case IndicatorStatus.empty:
              return const EmptyView();
            default:
              return const SizedBox();
          }
        },
      ),
    );
  }

  Widget _buildInviteeItem(InviteeInfo item) {
    return GroupInviteItemView(inviteeInfo: item);
  }
}
