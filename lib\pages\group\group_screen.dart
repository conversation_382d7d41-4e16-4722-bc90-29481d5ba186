import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/material.dart';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:logger/logger.dart';
import 'package:milestone/models/invitee_info.dart';
import 'package:milestone/pages/share/share_poster_view.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/navigation_route.dart';
import 'package:milestone/widget/background_scaffold.dart';

import '../../generated/assets.dart';
import '../../generated/l10n.dart';
import '../../models/team_summary.dart';
import '../../models/user_level.dart';
import '../../network/errors.dart';
import '../../network/network_api_client.dart';
import '../../r.dart';
import '../../widget/image_indicator.dart';
import 'group_invite_item_view.dart';
import 'group_invitee_list_view.dart';

class GroupScreen extends StatefulWidget {
  const GroupScreen({super.key});

  @override
  State<StatefulWidget> createState() => GroupScreenState();
}

class GroupScreenState extends State<GroupScreen>
    with SingleTickerProviderStateMixin {
  int _currentIndex = 0;
  TeamSummary teamSummary = TeamSummary();
  String? currentSortField; // 当前排序字段 null/childCount/numberCount/orderCount
  bool isSortAscending = false; // 当前排序方向

  void _handleSortTap(String field) {
    setState(() {
      if (currentSortField == field) {
        isSortAscending = !isSortAscending;
      }
      else {
        currentSortField = field;
        isSortAscending = false;
      }
    });
  }

  void _handleTap(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  void initState() {
    startLoading();
    super.initState();
  }

  Future<void> startLoading() async {
    try {
      TeamSummary result = await networkApiClient.getTeamSummary();
      Logger().d("response: $result");
      setState(() {
        teamSummary = result;
      });
    } catch (e) {
      if (context.mounted) {
        ApiErrorHandler.handleError(e, context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).my_team,
      backgroundImage: R.assetsImagesBgGroupTeam,
      titleColor: Colors.white,
      backgroundColor: backgroundColor,
      bottomNavigationBar: buildInviteButton(context),
      child: DefaultTabController(
        length: AgentFilters.values.length,
        child: ExtendedNestedScrollView(
          onlyOneScrollInBody: true,
          physics: const BouncingScrollPhysics(),
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return [buildHeaderContent(context)];
          },
          body: Container(
            decoration: BoxDecoration(color: Colors.white),
            child: TabBarView(
              children: AgentFilters.values.map((filter) {
                return GroupInviteeListView(agentFilters: filter, sortKey: currentSortField, isAsc: isSortAscending ? "DESC" : "ASC");
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildHeaderContent(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final leftPosition = 16.0;
    final width = (screenWidth - 16 * 2) / 2;
    return SliverToBoxAdapter(
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 15),
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(15)),
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 15),
                child: Column(
                  children: [
                    buildGroupIncomeView(context),
                    buildGroupIncomeDetailView(context),
                  ],
                ),
              ),
              AnimatedPositioned(
                curve: Curves.easeInOutCubic,
                duration: Duration(milliseconds: 100),
                left: _currentIndex == 0 ? leftPosition : width + leftPosition,
                top: 0,
                child: Stack(
                  children: [
                    Image.asset(R.assetsImagesBgGroupTeamSelect, height: 128, width: width),
                    Container(
                      height: 116,
                      width: width,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                        gradient: LinearGradient(
                          colors: [Color(0xFFF8E9D5), Color(0xFFECD0A9)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 15),
                child: Column(
                  children: [
                    buildGroupIncomeFrontView(context),
                    buildGroupIncomeDetailFrontView(context),
                  ],
                ),
              ),
            ],
          ),

          buildInviteStatusView(context),
          Container(
            padding: EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
            ),
            child: Column(
              children: [buildNavigationTabBar(), buildSortView(context)],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSortIcon(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset(R.assetsImagesIcArrowTop),
        Image.asset(R.assetsImagesIcBrandDown),
      ],
    );
  }

  Widget buildSortView(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      padding: EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Color(0xFFF5F5F5),
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          InkWell(
            onTap: () {
              _handleSortTap('childCount');
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  S.of(context).team_support,
                  style: TextStyle(
                    color: Color(0xFF98938F),
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                buildSortIcon(context),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              _handleSortTap('numberCount');
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  S.of(context).received_bonus,
                  style: TextStyle(
                    color: Color(0xFF98938F),
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                buildSortIcon(context),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              _handleSortTap('orderCount');
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  S.of(context).invite_time,
                  style: TextStyle(
                    color: Color(0xFF98938F),
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                buildSortIcon(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildNavigationTabBar() {
    return TabBar(
      isScrollable: false,
      dividerColor: Colors.transparent,
      indicatorSize: TabBarIndicatorSize.label,
      tabAlignment: TabAlignment.fill,
      labelColor: Color(0xFFC80D1F),
      labelStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
      dividerHeight: 44,
      unselectedLabelColor: unSelectedTextColor,
      unselectedLabelStyle: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w400,
      ),
      indicator: ImageIndicator(),
      tabs: AgentFilters.values
          .map((e) => Tab(height: 30, text: e.displayName(context)))
          .toList(),
    );
  }

  Widget buildInviteButton(BuildContext context) {
    return InkWell(
      onTap: () {
        customRouter(context, SharePosterView());
      },
      child: Container(
        height: 46,
        margin: EdgeInsets.only(
          right: 16,
          left: 16,
          top: 20,
          bottom: 8 + MediaQuery.of(context).padding.bottom,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFE6AC44), Color(0xFFE5B670)],
          ),
          borderRadius: BorderRadius.circular(30),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(Assets.imagesIcGroupInvite),
              SizedBox(width: 4),
              Text(
                S.of(context).invite_and_earn_money,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildGroupIncomeFrontView(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final leftPosition = 16.0;
    final width = (screenWidth - 16 * 2) / 2;
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () {
              _handleTap(0);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16),
              height: 100,
              width: screenWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Row(
                    children: [
                      Text(
                        S.of(context).income_pre_total_income,
                        style: TextStyle(
                          color: groupTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Image.asset(R.assetsImagesIcHomeQuestion),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        "Rp",
                        style: TextStyle(
                          color: groupTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Text(
                        teamSummary.estimatedCommission?.toStringAsFixed(0) ?? "0",
                        style: TextStyle(
                          color: groupTextColor,
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    "${S.of(context).income_today}+Rp${teamSummary.estimatedCommissionToday?.toStringAsFixed(0) ?? "0"}",
                    style: TextStyle(
                      color: groupTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Expanded(
          child: InkWell(
            onTap: () {
              _handleTap(1);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16),
              height: 100,
              width: screenWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Row(
                    children: [
                      Text(
                        S.of(context).income_pre_total_income,
                        style: TextStyle(
                          color: groupTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Image.asset(R.assetsImagesIcHomeQuestion),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        "Rp",
                        style: TextStyle(
                          color: groupTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Text(
                        teamSummary.actualCommission?.toStringAsFixed(0) ?? "0",
                        style: TextStyle(
                          color: groupTextColor,
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    "${S.of(context).income_today}+Rp${teamSummary.actualCommissionToday?.toStringAsFixed(0) ?? "0"}",
                    style: TextStyle(
                      color: groupTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildGroupIncomeDetailFrontView(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16),
            height: 100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Row(
                  children: [
                    Text(
                      "Rp",
                      style: TextStyle(
                        color: groupTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      teamSummary.inviteReward?.toStringAsFixed(0) ?? "0" ,
                      style: TextStyle(
                        color: groupTextColor,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                Text(
                  S.of(context).invite_bonus,
                  style: TextStyle(
                    color: groupTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Text(
                  "${S.of(context).income_today}+Rp${teamSummary.inviteRewardToday?.toStringAsFixed(0) ?? "0"}",
                  style: TextStyle(
                    color: groupTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ),
        Expanded(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16),
            height: 100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Row(
                  children: [
                    Text(
                      "Rp",
                      style: TextStyle(
                        color: groupTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      teamSummary.shoppingReward?.toStringAsFixed(0) ?? "0" ,
                      style: TextStyle(
                        color: groupTextColor,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                Text(
                  S.of(context).shopping_bonus,
                  style: TextStyle(
                    color: groupTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Text(
                  "${S.of(context).income_today}+Rp${teamSummary.shoppingRewardToday?.toStringAsFixed(0) ?? "0"}",
                  style: TextStyle(
                    color: groupTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget buildGroupIncomeView(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              color: Color(0xFFF5F3F2),
              borderRadius: BorderRadius.only(topLeft: Radius.circular(15)),
            ),
          ),
        ),
        VerticalDivider(
          color: Color(0xFFF2F2F2),
          thickness: 1,
          width: 1,
          indent: 16,
          endIndent: 16,
        ),
        Expanded(
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              color: Color(0xFFF5F3F2),
              borderRadius: BorderRadius.only(topRight: Radius.circular(15)),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildGroupIncomeDetailView(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(bottomLeft: Radius.circular(15)),
            ),
          ),
        ),
        VerticalDivider(
          color: Color(0xFFF2F2F2),
          thickness: 1,
          width: 1,
          indent: 16,
          endIndent: 16,
        ),
        Expanded(
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(bottomRight: Radius.circular(15)),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildInviteStatusView(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                S.of(context).cumulative_number_of_invitations,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: 4),
              Text(
                "${teamSummary.inviteCount ?? 0}",
                style: TextStyle(
                  color: Color(0xFFFB4143),
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Spacer(),
              Container(
                padding: EdgeInsets.symmetric(vertical: 2, horizontal: 4),
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xFF6F2F25), width: 1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Text(
                  "${S.of(context).today}+${teamSummary.inviteCountToday ?? 0}",
                  style: TextStyle(
                    color: primaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          Container(
            margin: EdgeInsets.symmetric(vertical: 16),
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(15)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).invite_agent,
                        style: TextStyle(
                          color: Color(0xFF98938F),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${teamSummary.inviteLevel1Count ?? 0}",
                                style: TextStyle(
                                  color: primaryTextColor,
                                  fontSize: 17,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                S.of(context).silver,
                                style: TextStyle(
                                  color: Color(0xFF98938F),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                "${teamSummary.inviteLevel2Count ?? 0}",
                                style: TextStyle(
                                  color: primaryTextColor,
                                  fontSize: 17,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                S.of(context).gold,
                                style: TextStyle(
                                  color: Color(0xFF98938F),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                "${teamSummary.inviteLevel3Count ?? 0}",
                                style: TextStyle(
                                  color: primaryTextColor,
                                  fontSize: 17,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                S.of(context).diamond,
                                style: TextStyle(
                                  color: Color(0xFF98938F),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                VerticalDivider(
                  color: Color(0xFF98938F),
                  thickness: 2,
                  width: 40,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).invite_normal_user,
                        style: TextStyle(
                          color: Color(0xFF98938F),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      SizedBox(height: 12),
                      Text(
                        "${teamSummary.inviteNormalCount ?? 0}",
                        style: TextStyle(
                          color: primaryTextColor,
                          fontSize: 17,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        S.of(context).normal_user,
                        style: TextStyle(
                          color: Color(0xFF98938F),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
