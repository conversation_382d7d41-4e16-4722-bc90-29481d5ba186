import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/vertical_dashed_divider.dart';

class GuideStep1Screen extends StatelessWidget {
  const GuideStep1Screen({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          children: [
            Image.asset(
              R.assetsImagesBgGuideStep1,
              fit: BoxFit.cover,
              width: MediaQuery.of(context).size.width,
            ),
            Row(
              children: [
                Image.asset(R.assetsImagesIcGuideStep1),
                Text(
                  S.of(context).guide_step1_title,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
        Si<PERSON><PERSON><PERSON>(height: 12),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: buildStepContent(context)
        ),
      ],
    );
  }

  Widget buildStepContent(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            _buildStepCircle("1", circleColor),
            const VerticalDashedDivider(height: 40),
            _buildStepCircle("2", circleColor),
            const VerticalDashedDivider(height: 40),
            _buildStepCircle("3", circleColor),
            const VerticalDashedDivider(height: 40),
            _buildStepCircle("4", circleColor),
          ],
        ),

        const SizedBox(width: 12),

        Expanded(
          child: Column(
            children: [
              _buildStepCard(
                context,
                bgImage: R.assetsImagesBgRebaseCashbackFlow1,
                text: S.of(context).guide_step1_content_flow_1,
                icons: R.assetsImagesIcTiktok14,
              ),
              const SizedBox(height: 12),
              _buildStepCard(
                context,
                bgImage: R.assetsImagesBgRebaseCashbackFlow2,
                text: S.of(context).guide_step1_content_flow_2,
                icons: R.assetsImagesIcLogo14,
              ),
              const SizedBox(height: 12),
              _buildStepCard(
                context,
                bgImage: R.assetsImagesBgRebaseCashbackFlow2,
                text: S.of(context).guide_step1_content_flow_3,
                icons: R.assetsImagesIcTiktok14,
              ),
              const SizedBox(height: 20),
              _buildStepCard(
                context,
                bgImage: R.assetsImagesBgRebaseCashbackFlow2,
                text: S.of(context).guide_step1_content_flow_4,
                icons: R.assetsImagesIcLogo14,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStepCircle(String label, Color color) {
    return CircleAvatar(
      backgroundColor: color,
      radius: 16,
      child: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }

  Widget _buildStepCard(
      BuildContext context, {
        required String bgImage,
        required String text,
        required String icons,
      }) {
    return Stack(
      children: [
        Image.asset(bgImage, fit: BoxFit.cover, width: double.infinity),
        Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Expanded(child: Text(text, style: const TextStyle(fontSize: 14))),
              const SizedBox(width: 8),
              Image.asset(icons),
            ],
          ),
        ),
      ],
    );
  }
}
