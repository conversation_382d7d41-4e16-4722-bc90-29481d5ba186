import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/pages/guide/step_introduction_view.dart';
import 'package:milestone/r.dart';

class GuideStepBaseView extends StatelessWidget {
  final String imageName;
  final String title;
  final String description;
  final String stepTitle;

  const GuideStepBaseView({
    super.key,
    required this.imageName,
    required this.title,
    required this.description,
    required this.stepTitle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StepIntroductionView(
          stepTitle: stepTitle,
          title: title,
          description: description,
        ),
        SizedBox(height: 20),
        buildContent(context)
      ],
    );
  }

  Widget buildContent(BuildContext context) {
    if(stepTitle.toLowerCase() == "video") {
      return Stack(
        alignment: AlignmentDirectional.center,
        children: [
          Image.asset(imageName),
          Image.asset(R.assetsImagesIcGuideStep6Play)
        ],
      );
    }else {
      return Image.asset(imageName);
    }
  }
}
