import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/pages/guide/guide_step_base_view.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:step_progress/step_progress.dart';

import 'guide_step_1_screen.dart';

class GuideStepContainerScreen extends StatelessWidget {
  final stepProgressController = StepProgressController(totalSteps: 5);
  GuideStepContainerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).usage_guideline_title,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        child: Column(
          children: [
            buildTopStepView(context),
            SizedBox(height: 20),
            CarouselSlider(
              options: CarouselOptions(
                height: MediaQuery.of(context).size.height / 3 * 2,
                autoPlay: false,
                viewportFraction: 1,
                enlargeCenterPage: false,
                autoPlayInterval: const Duration(seconds: 2),
                onPageChanged: (index, reason) {
                  Logger().d("index: $index, reason: $reason");
                  stepProgressController.setCurrentStep(index);
                },
              ),
              items: [
                GuideStep1Screen(),
                GuideStepBaseView(
                  imageName: R.assetsImagesImgGuideStep2Example,
                  title: S.of(context).guide_step1_content_flow_1_title,
                  description: S
                      .of(context)
                      .guide_step1_content_flow_1_description,
                  stepTitle: "Langkah 1",
                ),
                GuideStepBaseView(
                  imageName: R.assetsImagesImgGuideStep3Example,
                  title: S.of(context).guide_step1_content_flow_2_title,
                  description: S
                      .of(context)
                      .guide_step1_content_flow_2_description,
                  stepTitle: "Langkah 2",
                ),
                GuideStepBaseView(
                  imageName: R.assetsImagesImgGuideStep4Example,
                  title: S.of(context).guide_step1_content_flow_3_title,
                  description: S
                      .of(context)
                      .guide_step1_content_flow_3_description,
                  stepTitle: "Langkah 3",
                ),
                GuideStepBaseView(
                  imageName: R.assetsImagesImgGuideStep5Example,
                  title: S.of(context).guide_step1_content_flow_4_title,
                  description: S
                      .of(context)
                      .guide_step1_content_flow_4_description,
                  stepTitle: "Langkah 4",
                ),
                // GuideStepBaseView(
                //   imageName: R.assetsImagesImgGuideStep6Example,
                //   title: S.of(context).guide_step1_content_flow_5_title,
                //   description: S
                //       .of(context)
                //       .guide_step1_content_flow_5_description,
                //   stepTitle: "Video",
                // ),
              ],
            ),
            Spacer(),
            // buildBottomStepView(context),
          ],
        ),
      ),
    );
  }

  Widget buildTopStepView(BuildContext context) {
    return StepProgress(
      stepSize: 14,
      totalSteps: stepProgressController.totalSteps,
      padding: const EdgeInsets.all(10),
      controller: stepProgressController,
      highlightOptions: StepProgressHighlightOptions.highlightCurrentNode,
      theme: StepProgressThemeData(
        stepNodeStyle: StepNodeStyle(
          activeIcon: Container(
            width: 14,
            height: 14,
            decoration: BoxDecoration(
              color: stepCircleColor,
              border: Border.all(color: highlightTextColor, width: 2),
              borderRadius: BorderRadius.circular(7),
            ),
          ),
          icon: Container(
            width: 14,
            height: 14,
            decoration: BoxDecoration(
              color: stepCircleColor,
              border: Border.all(
                color: highlightTextColor.withValues(alpha: 0.3),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(7),
            ),
          ),
        ),
        stepLineStyle: StepLineStyle(lineThickness: 2),
      ),
    );
  }

  Widget buildBottomStepView(BuildContext context) {
    return StepProgress(
      totalSteps: stepProgressController.totalSteps,
      visibilityOptions: StepProgressVisibilityOptions.lineOnly,
      controller: stepProgressController,
      highlightOptions: StepProgressHighlightOptions.highlightCurrentLine,
      onStepChanged: (currentIndex) {
        debugPrint('onStepChanged: $currentIndex');
      },
      onStepLineTapped: (index) {
        debugPrint('onStepLineTapped: $index');
      },
      theme: StepProgressThemeData(
        stepLineSpacing: 6,
        defaultForegroundColor: highlightTextColor.withValues(alpha: 0.3),
        activeForegroundColor: highlightTextColor,
        stepLineStyle: StepLineStyle(
          lineThickness: 3,
          borderRadius: Radius.circular(2),
        ),
      ),
    );
  }
}
