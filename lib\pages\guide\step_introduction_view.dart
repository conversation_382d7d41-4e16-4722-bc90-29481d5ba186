import 'package:flutter/material.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';

class StepIntroductionView extends StatelessWidget {
  final String stepTitle;
  final String title;
  final String description;
  const StepIntroductionView({
    super.key,
    required this.stepTitle,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Stack(
                children: [
                  Image.asset(R.assetsImagesBgGuideStepMark),
                  Container(
                    padding: EdgeInsets.only(left: 12, top: 10),
                    child: Text(
                      stepTitle,
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    )
                  )
                ],
              ),
              Expanded(
                child: Center(
                  child: Image.asset(R.assetsImagesIcGuideStepDecoration),
                ),
              ),
            ],
          ),
          Container(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: primaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 16),
                Text(
                  description,
                  style: TextStyle(
                    color: secondaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
