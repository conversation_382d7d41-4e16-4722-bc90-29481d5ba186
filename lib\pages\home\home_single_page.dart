import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:logger/logger.dart';
import 'package:milestone/models/home_page_data.dart';
import 'package:milestone/models/product_list.dart';
import 'package:milestone/pages/detail/product_detail_screen.dart';
import 'package:milestone/pages/home/<USER>';
import 'package:milestone/pages/home/<USER>';
import 'package:milestone/widget/loading_error_view.dart';
import 'package:milestone/widget/loading_more_indicator.dart';

import '../../controller/network_refresh_mixin.dart';
import '../../controller/refresh_listener_mixin.dart';
import '../../models/home_platform.dart';
import '../../utils/navigation_route.dart';
import 'home_single_page_loading_more.dart';

class HomeSinglePage extends StatefulWidget {
  final Widget? headerContent;
  final int columnCount;
  final HomePlatform homePlatform;

  const HomeSinglePage({
    super.key,
    this.headerContent,
    this.columnCount = 2,
    required this.homePlatform,
  });

  @override
  State<StatefulWidget> createState() => HomeSinglePageState();
}

class HomeSinglePageState extends State<HomeSinglePage>
    with
        AutomaticKeepAliveClientMixin,
        NetworkRefreshMixin,
        RefreshListenerMixin {
  late ScrollController scrollController = ScrollController();
  late HomeSinglePageLoadingMoreAdapter loadingMoreController;
  late HomeSinglePageNotifier pageNotifier;
  bool isLoadingMore = true;
  bool isRequesting = false;
  bool isScrolling = false;
  bool isOffstageTop = true;
  double padding = 8;
  int columnCount = 2;
  Future? data;
  int page = 0;

  @override
  void initState() {
    pageNotifier = HomeSinglePageNotifier();
    pageNotifier.homePlatform = widget.homePlatform;
    loadingMoreController = HomeSinglePageLoadingMoreAdapter(pageNotifier);
    if (loadingMoreController.isEmpty) {
      data = startLoading(true);
    }
    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    loadingMoreController.dispose();
    super.dispose();
  }

  @override
  Widget buildNetworkIndicator(BuildContext context) {
    return Container();
  }

  @override
  Future<void> homeTabDoubleTapRefresh() async {
    startLoading(false);
  }

  @override
  Future<void> onNetworkRestored() async {
    startLoading(false);
  }

  Future startLoading(bool refresh) async {
    if (isRequesting) {
      return;
    }
    setState(() {
      isRequesting = true;
    });
    await loadingMoreController.refresh(refresh);
    setState(() {
      isLoadingMore = true;
      isRequesting = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return FutureBuilder(builder: buildHomeContent, future: data);
  }

  Widget buildHomeContent(BuildContext context, AsyncSnapshot snapshot) {
    switch (snapshot.connectionState) {
      case ConnectionState.none:
      case ConnectionState.waiting:
        return const Center(child: CupertinoActivityIndicator());
      case ConnectionState.active:
      case ConnectionState.done:
        if (snapshot.hasError) {
          return LoadingErrorView(
            error: "${snapshot.error}",
            onRetry: () {
              data = startLoading(true);
            },
          );
        }
        if (loadingMoreController.isEmpty && isRequesting) {
          return const Center(child: CupertinoActivityIndicator());
        } else {
          return buildContent(context);
        }
    }
  }

  Widget buildContent(BuildContext context) {
    double padding = 8;
    int columnCount = widget.columnCount;

    return MediaQuery.removePadding(
      removeTop: true,
      context: context,
      child: Stack(
        children: [
          //renderHeaderContent(),
          RefreshIndicator(
            child: Container(
              margin: EdgeInsets.only(left: padding, right: padding),
              child: LoadingMoreCustomScrollView(
                slivers: [
                  //renderHeaderContent(),
                  LoadingMoreSliverList(
                    SliverListConfig<Product>(
                      extendedListDelegate:
                          SliverWaterfallFlowDelegateWithFixedCrossAxisCount(
                            crossAxisCount: columnCount,
                            crossAxisSpacing: padding,
                            mainAxisSpacing: padding,
                            lastChildLayoutTypeBuilder: (index) =>
                                index == loadingMoreController.length
                                ? LastChildLayoutType.foot
                                : LastChildLayoutType.none,
                          ),
                      itemBuilder: (context, entity, index) {
                        return GestureDetector(
                          onTap: () {
                            customRouter(
                              context,
                              ProductDetailScreen(productId: entity.id),
                            );
                          },
                          child: HomeSingleProduct(
                            product: entity,
                            columnCount: columnCount,
                            key: Key("${entity.id}"),
                          ),
                        );
                      },
                      sourceList: loadingMoreController,
                      indicatorBuilder: (context, state) {
                        return LoadingMoreIndicator(
                          state,
                          tryAgainFunction: () {
                            data = startLoading(true);
                          },
                          emptyLinkFunction: () {
                            data = startLoading(true);
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            onRefresh: () async {
              await startLoading(true);
            },
          ),
          buildOffstageButton(),
        ],
      ),
    );
  }

  Widget buildOffstageButton() {
    return Positioned(
      bottom: 50,
      right: 20,
      child: Offstage(
        offstage: isOffstageTop,
        child: GestureDetector(
          onTap: () {
            scrollController.animateTo(
              0,
              duration: const Duration(seconds: 1),
              curve: Curves.ease,
            );
          },
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.arrow_upward, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget renderHeaderContent() {
    if (widget.headerContent != null) {
      return SliverToBoxAdapter(child: widget.headerContent);
    } else {
      return SliverToBoxAdapter(child: Container());
    }
  }

  @override
  bool get wantKeepAlive => true;
}
