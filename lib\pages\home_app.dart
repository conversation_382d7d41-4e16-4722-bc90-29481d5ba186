import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/language_manager.dart';
import 'package:provider/provider.dart';
import 'package:toastification/toastification.dart';

import 'main_screen.dart';

class HomeApp extends StatefulWidget {
  const HomeApp({super.key});

  @override
  State<HomeApp> createState() => _HomeAppState();
}

final easyLoading = EasyLoading.init();


class _HomeAppState extends State<HomeApp> {
  @override
  Widget build(BuildContext context) {
    EasyLoading.instance
      ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.light
      ..maskType = EasyLoadingMaskType.black
      ..dismissOnTap = false
      ..textColor = primaryTextColor
      ..indicatorColor = Colors.black
      ..backgroundColor = Colors.white;

    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return ToastificationWrapper(
          child: MaterialApp(
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              S.delegate,
            ],
            supportedLocales: S.delegate.supportedLocales,
            locale: languageManager.locale,
            title: "Genco",
            builder: (context, child) {
              child = easyLoading(context, child);
              return child;
            },
            home: Container(color: Colors.white, child: const MainScreen()),
          ),
        );
      },
    );
  }
}
