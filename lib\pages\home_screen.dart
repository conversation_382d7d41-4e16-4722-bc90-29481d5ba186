import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/home_platform.dart';
import 'package:milestone/models/product_info.dart';
import 'package:milestone/models/share_link_product_info.dart';
import 'package:milestone/network/errors.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/pages/guidelines/usage_guidelines_screen.dart';
import 'package:milestone/pages/me/faq_screen.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart'
    show
        primaryTextColor,
        thirdTextColor,
        fourthTextColor,
        unSelectedTextColor,
        backgroundColor;
import 'package:milestone/widget/bottom_product_info_view.dart';
import 'package:milestone/widget/image_indicator.dart';
import 'package:milestone/widget/loading_dialog.dart';
import 'package:milestone/widget/question_button.dart';
import 'package:url_launcher/url_launcher.dart';
import '../controller/event_bus_controller.dart';
import '../controller/network_refresh_mixin.dart';
import '../utils/navigation_route.dart';
import '../utils/toast_utils.dart';
import '../widget/common_dialog.dart';
import 'home/home_single_page.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with
        AutomaticKeepAliveClientMixin,
        WidgetsBindingObserver,
        NetworkRefreshMixin {
  final TextEditingController linkPastedTextEditController =
      TextEditingController();

  final FocusNode _focusNode = FocusNode();
  bool _hasCheckedClipboard = false;

  @override
  void initState() {
    super.initState();
    // 添加应用状态监听
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkClipboardForTikTokLink();
    });
  }

  Future<void> _checkClipboardForTikTokLink() async {
    if (_hasCheckedClipboard) return; // 确保只检查一次
    _hasCheckedClipboard = true;

    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      final String? clipboardText = data?.text;

      if (clipboardText != null && clipboardText.isNotEmpty) {
        // 检查是否是 TikTok 链接
        if (_isTikTokLink(clipboardText)) {
          // 延迟执行，确保页面完全构建完成
          await Future.delayed(const Duration(milliseconds: 300));
          _processLink(clipboardText);
        }
      }
    } catch (e) {
      Logger().e("Error checking clipboard: $e");
    }
  }

  bool _isTikTokLink(String link) {
    return link.startsWith("http");
  }

  bool isProcessed = false;
  Future<void> _processLink(String linkText) async {
    if (isProcessed) {
      return;
    }
    isProcessed = true;
    if (linkText.isEmpty) return;
    try {
      showLoadingDialog();
      ProductResponse result = await networkApiClient.searchLink(
        link: linkText,
      );
      Logger().d("response: $result");
      if (result.detailUrl != null && result.detailUrl!.isNotEmpty) {
        dismissLoadingDialog();
        isProcessed = false;
        final Uri uri = Uri.parse(result.detailUrl!);
        await launchUrl(uri, mode: LaunchMode.platformDefault);
        return;
      }

      if (result.productInfo?.id != null && context.mounted) {
        ShareLinkProductInfo productInfo = await networkApiClient
            .getProductShareInfo(result.productInfo!.id!);
        Logger().d("productInfo: ${productInfo.shareLink}");
        dismissLoadingDialog();
        isProcessed = false;
        if (productInfo.shareLink.isNotEmpty && context.mounted) {
          showBottomProductInfoView(context, productInfo, result);
          return;
        } else {
          String cashBackRateValue = result.productInfo?.cashBackRate ?? "0";
          double cashBackRate = double.parse(cashBackRateValue);
          if (cashBackRate == 0 && context.mounted) {
            showCustomDialog(
              context: context,
              title: S.of(context).cashback_is_0,
              description: S.of(context).cashback_is_0_content,
              confirmButtonText: S.of(context).confirm,
              cancelButtonText: S.of(context).cancel,
              onConfirm: (BuildContext dialogContext) async {
                showBottomProductInfoView(context, productInfo, result);
              },
              onCancel: (BuildContext dialogContext) {},
            );
          } else {
            EasyLoading.showToast("Link is empty");
          }
        }
      } else {
        dismissLoadingDialog();
        isProcessed = false;
        if (context.mounted) {
          showCustomDialog(
            context: context,
            title: S.of(context).product_link_empty,
            description: S.of(context).product_link_empty_content,
            confirmButtonText: S.of(context).confirm,
            onConfirm: (BuildContext dialogContext) async {
              Navigator.of(dialogContext).pop();
            },
          );
        }
      }
    } catch (e) {
      isProcessed = false;
      dismissLoadingDialog();
      if (context.mounted) {
        ApiErrorHandler.handleError(e, context);
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 当应用进入后台时关闭键盘
    if (state == AppLifecycleState.paused) {
      _unfocusKeyboard();
    }
  }

  void _unfocusKeyboard() {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
    }
  }

  @override
  void dispose() {
    // 移除监听器
    WidgetsBinding.instance.removeObserver(this);
    linkPastedTextEditController.dispose();
    _focusNode.dispose();
    // 确保在页面销毁时关闭键盘
    _unfocusKeyboard();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      // 添加手势检测器
      onTap: () {
        // 当点击页面任何地方时关闭键盘
        _unfocusKeyboard();
      },
      behavior: HitTestBehavior.opaque, // 确保整个页面区域可点击
      child: DefaultTabController(
        length: homePlatforms.length,
        child: Scaffold(
          backgroundColor: backgroundColor,
          body: ExtendedNestedScrollView(
            onlyOneScrollInBody: true,
            physics: const BouncingScrollPhysics(),
            headerSliverBuilder:
                (BuildContext context, bool innerBoxIsScrolled) {
                  return headerSilverBuilder(context, innerBoxIsScrolled);
                },
            body: builtTabBarView(context),
          ),
        ),
      ),
    );
  }

  List<Widget> headerSilverBuilder(
    BuildContext context,
    bool innerBoxIsScrolled,
  ) {
    return <Widget>[buildTopHeader(context), buildAppbar(context)];
  }

  Widget buildAppbar(BuildContext context) {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      centerTitle: true,
      floating: true,
      pinned: false,
      automaticallyImplyLeading: false,
      titleSpacing: 30,
      toolbarHeight: 30,
      flexibleSpace: Align(
        alignment: Alignment.centerLeft,
        child: TabBar(
          isScrollable: true,
          dividerColor: Colors.transparent,
          indicatorSize: TabBarIndicatorSize.tab,
          tabAlignment: TabAlignment.start,
          labelColor: primaryTextColor,
          labelStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
          unselectedLabelColor: unSelectedTextColor,
          unselectedLabelStyle: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w400,
          ),
          indicator: ImageIndicator(),
          tabs: homePlatforms
              .map((e) => Tab(height: 30, text: e.displayName(context)))
              .toList(),
        ),
      ),
    );
  }

  Widget buildTopHeader(BuildContext context) {
    double paddingTop = MediaQuery.of(context).padding.top;
    return SliverToBoxAdapter(
      child: Stack(
        children: [
          Image.asset(
            R.assetsImagesBgHomeTop,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Positioned(
            top: 130 + MediaQuery.of(context).padding.top,
            left: 12,
            right: 12,
            child: Image.asset(
              R.assetsImagesBgHomeCover,
              width: MediaQuery.of(context).size.width - 24,
              fit: BoxFit.cover,
            ),
          ),
          Column(
            children: [
              Container(
                padding: EdgeInsets.only(
                  left: 12,
                  right: 12,
                  top: paddingTop + 8,
                  bottom: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Image.asset(R.assetsImagesIcHomeLogo),
                    Text(
                      S.of(context).home_logo_slogan,
                      style: Theme.of(context).textTheme.titleMedium,
                      textAlign: TextAlign.left,
                    ),
                    Spacer(),
                    QuestionButton(
                      onPressed: () {
                        customRouter(context, FAQScreen());
                      },
                    ),
                  ],
                ),
              ),
              buildLinkPastedView(context),
              buildLinkPastedInstructView(context),
              SizedBox(height: 180),
              buildCashbackFlowView(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildLinkPastedView(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 4),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(19),
          border: Border.all(
            color: Colors.white, // 白色内描边
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 左侧搜索图标
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 8),
              child: Image.asset(
                R.assetsImagesIcHomeSearch,
                width: 16,
                height: 16,
              ),
            ),

            // 中间输入区域
            Expanded(
              child: TextField(
                focusNode: _focusNode,
                controller: linkPastedTextEditController,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: S.of(context).home_search_placeholder,
                  hintStyle: TextStyle(fontSize: 14, color: primaryTextColor),
                  isDense: true,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ),

            // 右侧渐变按钮
            Padding(
              padding: const EdgeInsets.all(6),
              child: SizedBox(
                height: 28,
                child: Material(
                  borderRadius: BorderRadius.circular(14),
                  clipBehavior: Clip.antiAlias,
                  child: InkWell(
                    onTap: () async {
                      String linkText = "";
                      if (linkPastedTextEditController.text.isEmpty) {
                        final ClipboardData? data = await Clipboard.getData(
                          Clipboard.kTextPlain,
                        );
                        linkPastedTextEditController.text = data?.text ?? "";
                        linkText = data?.text ?? "";
                      } else {
                        linkText = linkPastedTextEditController.text;
                      }

                      await _processLink(linkText);
                    },
                    child: Ink(
                      padding: const EdgeInsets.only(
                        left: 8,
                        right: 8,
                        top: 2,
                        bottom: 2,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(14),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0xFFC80D1F), Color(0xFFFB4143)],
                        ),
                      ),
                      child: Center(
                        child: Text(
                          S.of(context).home_search_button_title,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildLinkPastedInstructView(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 8),
      child: Stack(
        children: [
          Image.asset(
            R.assetsImagesBgHomePopover,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Container(
            padding: EdgeInsets.only(left: 0, right: 0, top: 10, bottom: 0),
            alignment: Alignment.center,
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: S.of(context).home_search_instructions_copy,
                    style: const TextStyle(
                      color: primaryTextColor,
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  TextSpan(text: ' '),
                  WidgetSpan(
                    child: Image.asset(R.assetsImagesIcHomeTiktok12),
                    alignment: PlaceholderAlignment.middle,
                  ),
                  TextSpan(text: ' '),
                  WidgetSpan(
                    child: Image.asset(R.assetsImagesIcHomeShopee12),
                    alignment: PlaceholderAlignment.middle,
                  ),
                  TextSpan(text: ' '),
                  TextSpan(
                    text: S.of(context).home_search_instructions_text,
                    style: const TextStyle(
                      color: primaryTextColor,
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildCashbackFlowView(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 0),
      child: Stack(
        children: [
          Image.asset(
            R.assetsImagesBgHomeCashbackFlow,
            width: MediaQuery.of(context).size.width - 12 * 2,
            fit: BoxFit.cover,
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 4, horizontal: 9),
            child: Column(
              children: [
                Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                        left: 18,
                        right: 0,
                        top: 5,
                        bottom: 0,
                      ),
                      child: Text(
                        S.of(context).home_cashback_instructions_title,
                        style: const TextStyle(
                          color: thirdTextColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: () {
                        customRouter(context, UsageGuidelinesScreen());
                      },
                      child: Row(
                        children: [
                          Text(
                            S.of(context).home_cashback_instructions_check_all,
                            style: const TextStyle(
                              color: fourthTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Image.asset(R.assetsImagesIcHomeCashbackFlowArrow),
                        ],
                      ),
                    ),
                  ],
                ),
                buildCashbackFlowSteps(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildCashbackFlowSteps(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 4, right: 4, top: 9, bottom: 8),
      child: Stack(
        children: [
          Positioned(
            top: 52,
            child: Image.asset(R.assetsImagesBgHomeCashbackFlowStep),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(R.assetsImagesIcHomeCashbackFlowStep1),
                  Image.asset(R.assetsImagesIcHomeCashbackFlowStepPoint1),
                  Text(
                    S.of(context).home_cashback_instructions_step1,
                    style: const TextStyle(
                      color: thirdTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(R.assetsImagesIcHomeCashbackFlowStep2),
                  Image.asset(R.assetsImagesIcHomeCashbackFlowStepPoint2),
                  Text(
                    S.of(context).home_cashback_instructions_step2,
                    style: const TextStyle(
                      color: thirdTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(R.assetsImagesIcHomeCashbackFlowStep3),
                  Image.asset(R.assetsImagesIcHomeCashbackFlowStepPoint3),
                  Text(
                    S.of(context).home_cashback_instructions_step3,
                    style: const TextStyle(
                      color: thirdTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(R.assetsImagesIcHomeCashbackFlowStep4),
                  Image.asset(R.assetsImagesIcHomeCashbackFlowStepPoint4),
                  Text(
                    S.of(context).home_cashback_instructions_step4,
                    style: const TextStyle(
                      color: thirdTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget builtTabBarView(BuildContext context) {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      children: homePlatforms.map((e) {
        return HomeSinglePage(homePlatform: e);
      }).toList(),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
