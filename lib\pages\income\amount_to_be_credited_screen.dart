import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/common_dialog.dart';

import '../../models/revenue_info.dart';

class AmountToBeCreditedScreen extends StatefulWidget {
  final RevenueInfo revenueInfo;
  const AmountToBeCreditedScreen({super.key, required this.revenueInfo});

  @override
  State<StatefulWidget> createState() => _AmountToBeCreditedScreenState();
}

class _AmountToBeCreditedScreenState extends State<AmountToBeCreditedScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          Image.asset(
            Assets.imagesBgCommon,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Column(
            children: [
              AppBar(
                elevation: 0,
                backgroundColor: Colors.transparent,
                centerTitle: true,
                leading: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Image.asset(Assets.imagesIcArrowBack),
                ),
                title: Text(
                  S.of(context).income_amount_to_be_credited,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: primaryTextColor,
                  ),
                ),
              ),

              Stack(
                children: [
                  Center(
                    child: Image.asset(
                      Assets.imagesBgCreditedTop,
                      width: MediaQuery.of(context).size.width - 12*2,
                      fit: BoxFit.cover,
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      showCustomDialog(
                        context: context,
                        title: S.of(context).income_amount_to_be_credited,
                        description: S
                            .of(context)
                            .income_amount_to_be_credited_hint,
                        confirmButtonText: S.of(context).ok,
                        onConfirm: (BuildContext dialogContext) {
                          Navigator.pop(dialogContext);
                        },
                      );
                    },
                    child: Container(
                      width: MediaQuery.of(context).size.width - 12*2,
                      padding: EdgeInsets.all(27),
                      margin: EdgeInsets.only(
                        top: 12,
                        left: 12,
                        right: 12,
                      ),
                      // decoration: BoxDecoration(
                      //   color: Colors.white,
                      //   borderRadius: BorderRadius.circular(16),
                      //   boxShadow: [
                      //     BoxShadow(
                      //       color: Colors.black.withValues(alpha: 0.1),
                      //       blurRadius: 12,
                      //       offset: Offset(0, 2),
                      //     ),
                      //   ],
                      // ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: "Rp",
                                  style: const TextStyle(
                                    color: highlightTextColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: "Gilroy",
                                  ),
                                ),
                                TextSpan(
                                  text: widget.revenueInfo.pendingAmount,
                                  style: const TextStyle(
                                    color: highlightTextColor,
                                    fontSize: 22,
                                    fontWeight: FontWeight.w700,
                                    fontFamily: "Gilroy",
                                  ),
                                ),
                              ],
                            ),
                            maxLines: 1,
                          ),
                          SizedBox(height: 20),
                          Row(children: [
                            Text(
                              S.of(context).income_amount_to_be_credited,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: primaryTextColor,
                              ),
                            ),
                            SizedBox(width: 4),
                            Image.asset(R.assetsImagesIcDetailQuestion)
                          ]),
                        ],
                      ),
                    ),
                  )
                ],
              ),

              buildRebaseIncomeView(context)
            ],
          ),
        ],
      ),
    );
  }

  Widget buildRebaseIncomeView(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        width: MediaQuery.of(context).size.width - 12*2,
        padding: EdgeInsets.all(21),
        margin: EdgeInsets.only(
          top: 12,
          left: 12,
          right: 12,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(children: [
              Image.asset(Assets.imagesIcCreditedRebase),
              SizedBox(width: 12),
              Text(
                S.of(context).credited_rebase_income,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: primaryTextColor,
                ),
              ),
              Spacer(),
              Text(
                "+Rp${widget.revenueInfo.pendingAmount}",
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: primaryTextColor,
                ),
              ),
              Image.asset(Assets.imagesIcIncomeArrowRight)
            ]),
          ],
        ),
      ),
    );
  }
}
