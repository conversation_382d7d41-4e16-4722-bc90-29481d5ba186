import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/revenue_info.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/common_dialog.dart';

class IncomeDetailScreen extends StatefulWidget {
  final RevenueInfo revenueInfo;
  const IncomeDetailScreen({super.key, required this.revenueInfo});

  @override
  State<IncomeDetailScreen>  createState() => _IncomeDetailScreenState();
}

class _IncomeDetailScreenState extends State<IncomeDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          Image.asset(
            Assets.imagesBgCommon,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Column(
            children: [
              AppBar(
                elevation: 0,
                backgroundColor: Colors.transparent,
                centerTitle: true,
                leading: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Image.asset(Assets.imagesIcArrowBack),
                ),
                title: Text(
                  S.of(context).income_income_detail,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: primaryTextColor,
                  ),
                ),
              ),
              buildOrderRebaseView(context, S.of(context).income_order_rebate, widget.revenueInfo.pendingAmount, widget.revenueInfo.receivedAmount),
              // buildOrderRebaseView(context, S.of(context).income_campaign_reward, "24.345.322", "24.345.322")
            ],
          ),
        ],
      ),
    );
  }

  Widget buildOrderRebaseView(BuildContext context, String title, String amountToRebase, String totalAmount) {
    return Container(
      width: MediaQuery.of(context).size.width - 12 * 2,
      padding: EdgeInsets.all(21),
      margin: EdgeInsets.only(top: 12, left: 12, right: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: title,
                  style: const TextStyle(
                    color: primaryTextColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextSpan(text: '  '),
                WidgetSpan(
                  child: Image.asset(Assets.imagesIcBrandHomeStar),
                  alignment: PlaceholderAlignment.top,
                ),
              ],
            ),
            textAlign: TextAlign.start,
          ),
          Divider(height: 30, indent: 0, color: backgroundColor),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              buildContent(context, S.of(context).income_expected_total_amount, S.of(context).income_expected_total_amount_hint, amountToRebase),
              buildContent(context, S.of(context).income_actual_credited_amount, S.of(context).income_actual_credited_amount_hint, amountToRebase)
            ],
          )
        ],
      ),
    );
  }

  Widget buildContent(BuildContext context, String title, String description, String amount) {
    return InkWell(
      onTap: () {
        showCustomDialog(
          context: context,
          title: title,
          description: description,
          confirmButtonText: S.of(context).ok,
          onConfirm: (BuildContext dialogContext) {
            Navigator.pop(dialogContext);
          },
        );
      },
      child: Column(
        children: [
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text:title,
                  style: const TextStyle(
                    color: secondaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(text: '  '),
                WidgetSpan(
                  child: Image.asset(Assets.imagesIcDetailQuestion),
                  alignment: PlaceholderAlignment.middle,
                ),
              ],
            ),
            textAlign: TextAlign.start,
          ),
          SizedBox(height: 12),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text:"Rp",
                  style: const TextStyle(
                    color: primaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text:amount,
                  style: const TextStyle(
                    color: primaryTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.start,
          ),
        ],
      ),
    );
  }
}