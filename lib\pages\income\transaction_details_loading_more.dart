import 'dart:async';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/models/transaction_detail_model.dart';
import 'package:uuid/uuid.dart';

import '../../models/transaction_detail_filter.dart';
import '../../models/transaction_item.dart';
import '../../network/network_api_client.dart';

class TransactionDetailsLoadingMoreAdapter extends LoadingMoreBase<TransactionItem> {
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  TransactionDetailFilter filter = TransactionDetailFilter.all;

  @override
  bool get hasMore => _hasMore;

  @override
  Future<bool> loadData([bool isLoadMoreAction = false]) async {
    if (!isLoadMoreAction) {
      clear();
      _page = 0;
    }
    TransactionDetailModel transactionDetailModel = await networkApiClient.getTransactionDetailList(type: filter.value());
    for (var group in transactionDetailModel.list) {
      addAll(group.list);
    }
    _page++;
    _hasMore = transactionDetailModel.page < transactionDetailModel.totalPage;

    return true;
  }
}
