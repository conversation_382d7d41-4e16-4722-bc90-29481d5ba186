import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/transaction_detail_filter.dart';
import 'package:milestone/pages/income/transaction_detail_list_view.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/filter_tab.dart';

class TransactionDetailsScreen extends StatefulWidget {
  const TransactionDetailsScreen({super.key});

  @override
  State<StatefulWidget> createState() => _TransactionDetailsScreenState();
}

class _TransactionDetailsScreenState extends State<TransactionDetailsScreen>
    with SingleTickerProviderStateMixin {
  double navigationPadding = 44;
  late TabController tabController;

  @override
  initState() {
    super.initState();
    tabController = TabController(
      length: transactionDetailFilters.length,
      vsync: this,
    );
  }

  @override
  dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          Image.asset(
            Assets.imagesBgCommon,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          DefaultTabController(
            length: transactionDetailFilters.length,
            child: Column(
              children: [
                AppBar(
                  elevation: 0,
                  backgroundColor: Colors.transparent,
                  centerTitle: true,
                  leading: IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Image.asset(Assets.imagesIcArrowBack),
                  ),
                  title: Text(
                    S.of(context).income_transaction_detail,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: primaryTextColor,
                    ),
                  ),
                ),
                SizedBox(
                  height: navigationPadding,
                  child: buildNavigationTabBar(),
                ),
                Expanded(
                  child: TabBarView(
                    controller: tabController,
                    children: transactionDetailFilters.map((filter) {
                      return TransactionDetailListView(filter: filter);
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildNavigationTabBar() {
    return TabBar(
      controller: tabController,
      isScrollable: false,
      dividerColor: Colors.transparent,
      indicatorColor: Colors.transparent, // 关键修复
      automaticIndicatorColorAdjustment: true,
      indicator: BoxDecoration(
        color: selectedTabColor,
        borderRadius: const BorderRadius.all(Radius.circular(20)),
      ),
      // 添加未选中状态的背景色为透明
      unselectedLabelColor: unSelectedTextColor,
      labelPadding: const EdgeInsets.only(left: 7, top: 0, right: 7, bottom: 0),
      indicatorSize: TabBarIndicatorSize.tab,
      indicatorPadding: const EdgeInsets.only(
        left: 5,
        top: 5,
        right: 5,
        bottom: 5,
      ),
      labelColor: Colors.white,
      labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      unselectedLabelStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      // 确保标签本身的背景是透明的
      overlayColor: MaterialStateProperty.all(Colors.transparent),
      tabs: createCategoryList(context, tabController).map((tab) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.transparent, // 每个标签的背景设为透明
          ),
          child: tab,
        );
      }).toList(),
    );
  }

  List<Widget> createCategoryList(
    BuildContext context,
    TabController controller,
  ) {
    return List.generate(transactionDetailFilters.length, (index) {
      final filter = transactionDetailFilters[index];
      return FilterTab(filter: filter, controller: controller, index: index);
    });
  }
}
