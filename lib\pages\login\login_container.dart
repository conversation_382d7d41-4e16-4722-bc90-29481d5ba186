import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:milestone/main.dart';
import 'package:milestone/pages/login/bind_account_and_password_screen.dart';
import 'package:milestone/pages/login/login_with_password_screen.dart';
import 'package:milestone/pages/login/login_with_tiktok_screen.dart';
import 'package:milestone/pages/login/login_with_whatsapp_screen.dart';
import 'package:milestone/pages/login/verification_code_screen.dart';
import 'package:milestone/utils/cartoon.dart';
import 'package:milestone/utils/config.dart';

import '../me/faq_screen.dart';
import '../me/privacy_screen.dart';
import '../me/term_of_service_screen.dart';

class LoginContainer extends StatelessWidget {
  const LoginContainer({super.key});

  @override
  Widget build(BuildContext context) {
    String initialRoute = "login_with_whatsapp"; // Global.isDebugMode ? "login_with_tiktok" : "login_with_whatsapp"; //"login_with_whatsapp";
    return Navigator(
      initialRoute: initialRoute,
      onGenerateRoute: (RouteSettings settings) {
        final arguments = settings.arguments as Map<String, dynamic>? ?? {};

        switch (settings.name) {
          case "login_with_whatsapp":
            return buildPageRoute(LoginWithWhatsappScreen(), settings);
          case "login_with_tiktok":
            return buildPageRoute(LoginWithTikTokScreen(), settings);
          case "login_with_password":
            final phoneNumber = arguments['phoneNumber'] as String? ?? '';
            return buildPageRoute(
              LoginWithPasswordScreen(phoneNumber: phoneNumber),
              settings,
            );
          case "verification_code":
            final phoneNumber = arguments['phoneNumber'] as String? ?? '';
            return buildPageRoute(
              VerificationCodeScreen(phoneNumber: phoneNumber),
              settings,
            );
          case "bind_account_and_password":
            final phoneNumber = arguments['phoneNumber'] as String? ?? '';
            final captcha = arguments['captcha'] as String? ?? '';
            return buildPageRoute(
              BindAccountAndPasswordScreen(
                phoneNumber: phoneNumber,
                captcha: captcha,
              ),
              settings,
            );
          case "privacy":
            return buildPageRoute(PrivacyScreen(), settings);
          case "user_agreement":
            return buildPageRoute(TermOfServiceScreen(), settings);
          default:
            return buildPageRoute(LoginWithWhatsappScreen(), settings);
        }
      },
    );
  }

  PageRoute buildPageRoute(Widget page, RouteSettings settings) {
    if (Platform.isIOS) {
      return CupertinoPageRoute(builder: (_) => page, settings: settings);
    } else {
      return CustomRouteSlide(page, settings: settings);
    }
  }
}
