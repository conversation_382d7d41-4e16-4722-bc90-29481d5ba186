import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/config.dart';
import 'package:milestone/utils/tiktok_login.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:tiktok_sdk_v2/tiktok_sdk_v2.dart';

import '../../models/user_info.dart';
import '../../utils/user_utils.dart';
import '../../widget/loading_dialog.dart';
import '../main_screen.dart';

class LoginWithTikTokScreen extends StatefulWidget {
  const LoginWithTikTokScreen({super.key});

  @override
  State<LoginWithTikTokScreen> createState() => _LoginWithTikTokScreen();
}

class _LoginWithTikTokScreen extends State<LoginWithTikTokScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.white,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leading: IconButton(
          onPressed: () {
            Navigator.of(context, rootNavigator: true).pop();
          },
          icon: Image.asset(R.assetsImagesIcCloseButton),
        ),
      ),
      body: Stack(
        children: [
          Image.asset(
            R.assetsImagesBgLogin,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width,
          ),
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 18),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Image.asset(R.assetsImagesLogo70, width: 30, height: 30),
                      SizedBox(height: 16),
                      Text(
                        S.of(context).login_title,
                        style: TextStyle(
                          color: primaryTextColor,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        S.of(context).login_subtitle,
                        style: TextStyle(
                          color: primaryTextColor,
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 100),
                InkWell(
                  onTap: () async {
                    Logger().d("Start login");
                    try {
                      TikTokLoginResult result = await TiktokLogin.login();
                      Logger().d("TikTok login result: $result");
                      if (result.authCode == null || result.codeVerifier == null) {
                        makeToast(S.of(context).login_failed);
                        return;
                      }
                      showLoadingDialog();
                      Response response =  await networkApiClient.thirdPartyLoginLogin(code: result.authCode!, codeVerifier: result.codeVerifier!);
                      if (response.statusCode == 200) {
                        final Map<String, dynamic> responseBody = response.data is Map
                            ? response.data as Map<String, dynamic>
                            : json.decode(response.data);
                        if (responseBody['code'] == 200) {
                          UserManager.saveUserInfo(responseBody["data"]);
                          UserInfo userInfo = await networkApiClient.getUserInfo();
                          UserManager.saveFullUserInfo(userInfo);
                          dismissLoadingDialog();
                          if(context.mounted) {
                            makeToast(S.of(context).login_success);
                            Navigator.of(context, rootNavigator: true).pop();
                          }
                          return;
                        } else {
                          if(context.mounted) {
                            dismissLoadingDialog();
                            makeToast("${S
                                .of(context)
                                .login_failed}: ${responseBody['message']}");
                          }
                        }
                      } else {
                        dismissLoadingDialog();
                        if(context.mounted) {
                          makeToast("${S
                              .of(context)
                              .login_failed}, ${response.statusMessage}");
                        }
                      }
                    } on Exception catch (e) {
                      dismissLoadingDialog();
                      Logger().e("TikTok login error: $e");
                      makeToast(e.toString());
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 22),
                    width: MediaQuery.of(context).size.width,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Color(0xFF3C3C3C), Color(0xFF2A2A2A)],
                      ),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(R.assetsImagesIcTiktok20),
                        SizedBox(width: 8),
                        Text(
                          S.of(context).login_with_tiktok,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
