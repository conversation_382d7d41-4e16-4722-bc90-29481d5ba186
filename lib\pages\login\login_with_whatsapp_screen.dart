import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/utils/web_view_utils.dart';
import 'package:milestone/widget/loading_dialog.dart';
import 'package:tiktok_sdk_v2/tiktok_sdk_v2.dart';

import '../../models/user_info.dart';
import '../../utils/tiktok_login.dart';
import '../../utils/user_utils.dart';

class LoginWithWhatsappScreen extends StatefulWidget {
  const LoginWithWhatsappScreen({super.key});

  @override
  State<LoginWithWhatsappScreen> createState() =>
      _LoginWithWhatsappScreenState();
}

class _LoginWithWhatsappScreenState extends State<LoginWithWhatsappScreen> {
  String phoneNumber = "";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.white,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leading: IconButton(
          onPressed: () {
            Navigator.of(context, rootNavigator: true).pop();
          },
          icon: Image.asset(R.assetsImagesIcCloseButton),
        ),
      ),
      body: Stack(
        children: [
          Image.asset(
            R.assetsImagesBgLogin,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width,
          ),
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 18),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Image.asset(R.assetsImagesLogo70, width: 30, height: 30),
                      SizedBox(height: 16),
                      Text(
                        S.of(context).login_title,
                        style: TextStyle(
                          color: primaryTextColor,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        S.of(context).login_subtitle,
                        style: TextStyle(
                          color: primaryTextColor,
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 45),
                buildContentView(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildContentView(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFFFFF7E9), Colors.white],
          begin: Alignment.topCenter,
          end: Alignment(0.0, -0.8),
        ),
        borderRadius: BorderRadius.vertical(top: Radius.circular(35)),
      ),
      child: Column(
        children: [
          SizedBox(height: 25),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 22),
            width: MediaQuery.of(context).size.width,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).phone_number,
                  style: TextStyle(
                    color: secondaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                SizedBox(height: 8),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 0, horizontal: 12),
                  decoration: BoxDecoration(
                    color: Color(0xFFF9F8F7),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Row(
                    children: [
                      Image.asset(R.assetsImagesIcIndonesia),
                      SizedBox(width: 8),
                      Text(
                        "+62",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: primaryTextColor,
                        ),
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          keyboardType: TextInputType.phone,
                          onChanged: (value) async {
                            setState(() {
                              phoneNumber = value;
                            });
                          },
                          decoration: InputDecoration(
                            hintText: S.of(context).phone_number_placeholder,
                            border: InputBorder.none,
                            hintStyle: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: secondaryTextColor,
                            ),
                          ),
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: primaryTextColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 40),

          InkWell(
            onTap: () async {
              if (phoneNumber.trim().isEmpty) {
                makeToast(S.of(context).account_empty_hint);
                return;
              }
              try {
                showLoadingDialog();
                Response response = await networkApiClient.sendVerificationCode(
                  phoneNumber,
                );
                dismissLoadingDialog();

                if (response.statusCode == 200) {
                  final Map<String, dynamic> responseBody = response.data is Map
                      ? response.data as Map<String, dynamic>
                      : json.decode(response.data);
                  // TODO: 暂时先不验证
                  // if (responseBody['code'] == 200) {
                  makeToast(S.of(context).login_code_sent);
                  Navigator.pushNamed(
                    context,
                    "verification_code",
                    arguments: {'phoneNumber': phoneNumber},
                  );
                  // } else {
                  //   makeToast(responseBody['message']);
                  // }
                } else {
                  makeToast(S.of(context).login_send_failed);
                }
              } catch (e) {
                makeToast(e.toString());
                dismissLoadingDialog();
                return;
              }
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 22),
              width: MediaQuery.of(context).size.width,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: phoneNumber.trim().isNotEmpty
                      ? [Color(0xFFE6AC44), Color(0xFFFACD8A)]
                      : [Color(0x7FE6AC44), Color(0x7FE5B670)],
                ),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Center(
                child: Text(
                  S.of(context).next_step,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 35),
          Row(
            children: [
              Expanded(child: Divider(color: secondaryTextColor, indent: 20)),
              SizedBox(width: 6),
              Text(
                S.of(context).login_with_other_method,
                style: TextStyle(
                  color: secondaryTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              SizedBox(width: 6),
              Expanded(
                child: Divider(color: secondaryTextColor, endIndent: 20),
              ),
            ],
          ),
          SizedBox(height: 16),
          InkWell(
            onTap: () async {
              Logger().d("Start login");
              try {
                TikTokLoginResult result = await TiktokLogin.login();
                Logger().d("TikTok login result: $result");
                if (result.authCode == null || result.codeVerifier == null) {
                  makeToast(S.of(context).login_failed);
                  return;
                }
                showLoadingDialog();
                Response response = await networkApiClient.thirdPartyLoginLogin(
                  code: result.authCode!,
                  codeVerifier: result.codeVerifier!,
                );
                if (response.statusCode == 200) {
                  final Map<String, dynamic> responseBody = response.data is Map
                      ? response.data as Map<String, dynamic>
                      : json.decode(response.data);
                  if (responseBody['code'] == 200) {
                    UserManager.saveUserInfo(responseBody["data"]);
                    UserInfo userInfo = await networkApiClient.getUserInfo();
                    UserManager.saveFullUserInfo(userInfo);
                    dismissLoadingDialog();
                    if (context.mounted) {
                      makeToast(S.of(context).login_success);
                      Navigator.of(context, rootNavigator: true).pop();
                    }
                    return;
                  } else {
                    if (context.mounted) {
                      dismissLoadingDialog();
                      makeToast(
                        "${S.of(context).login_failed}: ${responseBody['message']}",
                      );
                    }
                  }
                } else {
                  dismissLoadingDialog();
                  if (context.mounted) {
                    makeToast(
                      "${S.of(context).login_failed}, ${response.statusMessage}",
                    );
                  }
                }
              } on Exception catch (e) {
                dismissLoadingDialog();
                Logger().e("TikTok login error: $e");
                makeToast(e.toString());
              }
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 22),
              width: MediaQuery.of(context).size.width,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFF3C3C3C), Color(0xFF2A2A2A)],
                ),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(R.assetsImagesIcTiktok20),
                  SizedBox(width: 8),
                  Text(
                    S.of(context).login_with_tiktok,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 30),
          Center(
            child: Wrap(
              alignment: WrapAlignment.center, // 保持内容居中
              crossAxisAlignment: WrapCrossAlignment.center, // 垂直居中对齐
              spacing: 4, // 子组件之间的水平间距
              children: [
                Text(
                  S.of(context).login_agreement,
                  style: TextStyle(
                    color: secondaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pushNamed("user_agreement");
                  },
                  child: Text(
                    S.of(context).user_agreement,
                    style: TextStyle(
                      color: primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                Text(
                  S.of(context).and,
                  style: TextStyle(
                    color: secondaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pushNamed("privacy");
                  },
                  child: Text(
                    S.of(context).privacy_policy,
                    style: TextStyle(
                      color: primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
