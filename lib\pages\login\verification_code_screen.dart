import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/pages/login/login_with_password_screen.dart';
import 'package:milestone/pages/main_screen.dart';
import 'package:milestone/pages/me/faq_screen.dart';
import 'package:milestone/pages/me/privacy_screen.dart';
import 'package:milestone/pages/verification_box/verification_box.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/loading_dialog.dart';
import '../../models/user_info.dart';

import 'bind_account_and_password_screen.dart';

class VerificationCodeScreen extends StatefulWidget {
  final String phoneNumber;
  final bool needUpdatePassword;
  const VerificationCodeScreen({
    super.key,
    required this.phoneNumber,
    this.needUpdatePassword = false,
  });

  @override
  State<StatefulWidget> createState() => _VerificationCodeScreenState();
}

class _VerificationCodeScreenState extends State<VerificationCodeScreen> {
  bool buttonEnabled = false;
  int _countdownSeconds = 60;
  late Timer _timer;
  String _verificationCode = '';

  @override
  void initState() {
    super.initState();
    _startTimer(); // 启动倒计时
  }

  @override
  void dispose() {
    _timer.cancel(); // 销毁计时器
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdownSeconds > 0) {
        setState(() {
          _countdownSeconds--;
        });
      } else {
        setState(() {
          buttonEnabled = true;
        });
        timer.cancel();
      }
    });
  }

  Future<void> _resendVerificationCode(BuildContext context) async {
    // 实现实际的重新发送验证码逻辑
    Logger().d("Resending verification code to ${widget.phoneNumber}");
    setState(() {
      buttonEnabled = false;
    });
    Response response = await networkApiClient.sendVerificationCode(
      widget.phoneNumber,
    );
    if (response.statusCode == 200) {
      final Map<String, dynamic> responseBody = response.data is Map
          ? response.data as Map<String, dynamic>
          : json.decode(response.data);
      if (responseBody['code'] == 200) {
        setState(() {
          _countdownSeconds = 60;
        });
        _startTimer();
        return;
      } else {
        if (!context.mounted) return;
        makeToast("${S.of(context).login_failed}: ${responseBody['message']}");
      }
    } else {
      if (!context.mounted) return;
      makeToast(
        "${S.of(context).login_send_failed}, ${response.statusMessage}",
      );
    }
    setState(() {
      buttonEnabled = true;
    });
  }

  Future<void> _handleLogin(BuildContext context) async {
    //  实现实际的登录逻辑
    Logger().d("Logging in with code: $_verificationCode");
    if (_verificationCode.isEmpty || _verificationCode.length != 6) {
      makeToast(S.of(context).input_opt_verification_code_error);
      return;
    }
    setState(() {
      buttonEnabled = false; // 禁用按钮，防止重复点击
    });
    try {
      showLoadingDialog();
      Response response = await networkApiClient.mobileLogin(
        phone: widget.phoneNumber,
        captcha: _verificationCode,
      );

      Logger().d("Login response: $response, ${response.statusMessage}");

      if (!context.mounted) {
        dismissLoadingDialog();
        return;
      }
      if (response.statusCode == 200) {
        // 解析响应数据
        final Map<String, dynamic> responseBody = response.data is Map
            ? response.data as Map<String, dynamic>
            : json.decode(response.data);
        if (responseBody['code'] == 200) {
          UserManager.saveUserInfo(responseBody["data"]);
          UserInfo userInfo = await networkApiClient.getUserInfo();
          UserManager.saveFullUserInfo(userInfo);

          dismissLoadingDialog();

          if (responseBody["data"]["isNewUser"] || widget.needUpdatePassword) {
            Navigator.pushNamed(
              context,
              "bind_account_and_password",
              arguments: {
                'phoneNumber': widget.phoneNumber,
                'captcha': _verificationCode,
              },
            );
          } else {
            dismissLoadingDialog();
            makeToast(S.of(context).login_success);
            Navigator.of(context, rootNavigator: true).pop();
          }
          return;
        } else {
          dismissLoadingDialog();
          makeToast(
            "${S.of(context).login_failed}: ${responseBody['message']}",
          );
        }
      } else {
        dismissLoadingDialog();
        makeToast("${S.of(context).login_failed}, ${response.statusMessage}");
      }
      setState(() {
        buttonEnabled = true;
      });
    } catch (e) {
      dismissLoadingDialog();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: "",
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              S.of(context).input_opt_verification_code,
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.w600,
                color: primaryTextColor,
              ),
            ),
            SizedBox(height: 12),
            Text(
              S
                  .of(context)
                  .input_opt_verification_code_hint(widget.phoneNumber),
              style: TextStyle(
                fontSize: 13,
                color: secondaryTextColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(height: 25),
            Text(
              "OPT",
              style: TextStyle(
                fontSize: 13,
                color: secondaryTextColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(height: 12),
            VerificationBox(
              textStyle: TextStyle(
                fontSize: 22,
                color: primaryTextColor,
                fontWeight: FontWeight.w600,
              ),
              onSubmitted: (value) {
                setState(() {
                  _verificationCode = value;
                  buttonEnabled = value.length == 6;
                });
              },
            ),
            SizedBox(height: 22),
            InkWell(
              onTap: () async {
                if (!buttonEnabled) {
                  return;
                }
                if (_verificationCode.isNotEmpty &&
                    _verificationCode.length == 6) {
                  _handleLogin(context);
                }
                if (_countdownSeconds <= 0) {
                  _resendVerificationCode(context);
                }
              },
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 0),
                width: MediaQuery.of(context).size.width,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: buttonEnabled
                        ? [Color(0xFFE6AC44), Color(0xFFFACD8A)]
                        : [Color(0x7FE6AC44), Color(0x7FE5B670)],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Text(
                    updateDisplayText(context),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 12),
            widget.needUpdatePassword
                ? Container()
                : Center(
                    child: TextButton(
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          "login_with_password",
                          arguments: {'phoneNumber': widget.phoneNumber},
                        );
                      },
                      child: Text(
                        S.of(context).login_with_password,
                        style: TextStyle(
                          color: primaryTextColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
            SizedBox(height: 30),
            Center(
              child: Wrap(
                alignment: WrapAlignment.center, // 保持内容居中
                crossAxisAlignment: WrapCrossAlignment.center, // 垂直居中对齐
                spacing: 4, // 子组件之间的水平间距
                children: [
                  Text(
                    S.of(context).login_agreement,
                    style: TextStyle(
                      color: secondaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pushNamed("user_agreement");
                    },
                    child: Text(
                      S.of(context).user_agreement,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  Text(
                    S.of(context).and,
                    style: TextStyle(
                      color: secondaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pushNamed("privacy");
                    },
                    child: Text(
                      S.of(context).privacy_policy,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String updateDisplayText(BuildContext context) {
    if (_verificationCode.isNotEmpty && _verificationCode.length == 6) {
      return widget.needUpdatePassword
          ? S.of(context).modify_password
          : S.of(context).login;
    }
    if (_countdownSeconds > 0) {
      return "${S.of(context).resend_in} (${_countdownSeconds}s)";
    } else {
      return S.of(context).resend_code;
    }
  }
}
