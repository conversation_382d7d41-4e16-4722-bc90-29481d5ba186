import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/controller/event_bus_controller.dart';
import 'package:milestone/main.dart';
import 'package:milestone/pages/login/login_container.dart';
import 'package:milestone/pages/login/login_with_tiktok_screen.dart';
import 'package:milestone/utils/navigation_route.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:milestone/widget/bottom_tabbar/blur_nav_bar.dart';
import 'package:milestone/widget/bottom_tabbar/blur_nav_bar_item.dart';
import 'package:milestone/widget/bottom_tabbar/tab_item.dart';

import '../controller/refresh_listener_mixin.dart';
import '../mock/product_list_mock.dart';
import 'brand/brand_home_screen.dart';
import 'home/home_single_page.dart';
import 'home/home_single_product.dart';
import 'home_screen.dart';
import 'income/income_home_screen.dart';
import 'login/bind_account_and_password_screen.dart';
import 'login/login_with_whatsapp_screen.dart';
import 'me/me_screen.dart';
import 'me/mobile_login_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<StatefulWidget> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  int _currentIndex = 0;
  late TabController tabController;
  late AnimationController animationController;
  bool isTabBarVisible = false;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: homeTabItems.length, vsync: this);
    tabController.addListener(() {
      setState(() {
        isTabBarVisible = tabController.index != 0;
      });
    });
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
  }

  @override
  void dispose() {
    tabController.dispose();
    animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: homeTabItems.length,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        bottomNavigationBar: BlurNavbar(
          fontSize: 12,
          iconSize: 28,
          currentIndex: _currentIndex,
          onTap: (index) {
            Logger().d("click $index");
            if (index == 0) {
              Logger().d("click home");
            } else if ((index == 2 || index == 3) && !isLogin()) {
              customRouter(context, LoginContainer());
              return;
            }
            tabController.animateTo(index);
            setState(() {
              _currentIndex = index;
            });
          },
          onDoubleTap: (index) {
            Logger().d("send event");
            eventBus.fire(HomeRefreshEvent(index));
          },
          items: homeTabItems.map(buildNavbarItem).toList(),
        ),
        body: TabBarView(
          controller: tabController,
          physics: const NeverScrollableScrollPhysics(),
          children: homeTabItems.map((tabItem) {
            switch (tabItem) {
              case HomeTabItem.home:
                return const HomeScreen();
              case HomeTabItem.brand:
                return BrandHomeScreen();
              case HomeTabItem.income:
                return const IncomeHomeScreen();
              case HomeTabItem.mine:
                return MeScreen();
            }
          }).toList(),
        ),
      ),
    );
  }

  BlurNavbarItem buildNavbarItem(HomeTabItem tabItem) {
    return BlurNavbarItem(
      title: tabItem.displayName(context),
      selectIcon: tabItem.selectIcon(context),
      icon: tabItem.icon,
    );
  }
}
