import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:markdown_widget/config/all.dart';
import 'package:markdown_widget/markdown_widget.dart';
import 'package:markdown_widget/widget/blocks/leaf/paragraph.dart';
import 'package:markdown_widget/widget/markdown.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    String languageCode = Localizations.localeOf(context).languageCode;
    return BackgroundScaffold(
      title: S.of(context).about,
      backgroundImage: R.assetsImagesBgAbout,
      backgroundColor: Colors.white,
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            Image.asset(R.assetsImagesLogo70),
            SizedBox(height: 8),
            FutureBuilder(
              future: getAppVersion(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(
                    child: Text('Loading Failed: ${snapshot.error.toString()}'),
                  );
                }
                return Text(
                  snapshot.data ?? "----",
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                    color: primaryTextColor,
                  ),
                );
              },
            ),
            SizedBox(height: 8),
            FutureBuilder(
              future: loadAboutContent(languageCode),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(
                    child: Text('Loading Failed: ${snapshot.error.toString()}'),
                  );
                }
                return Expanded(
                  child: Container(
                    padding: EdgeInsets.all(12),
                    child: MarkdownWidget(
                      data: snapshot.data ?? "",
                      config: MarkdownConfig(
                        configs: [
                          PConfig(
                            textStyle: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                              color: secondaryTextColor,
                            ),
                          ),
                          H3Config(
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: primaryTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<String> getAppVersion() async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();

    String appName = packageInfo.appName;
    String packageName = packageInfo.packageName;
    String version = packageInfo.version; // 例如: 1.0.0
    String buildNumber = packageInfo.buildNumber; // 例如: 1

    print("App Name: $appName");
    print("Version: $version");
    print("Build Number: $buildNumber");
    return "V $version-$buildNumber";
  }

  Future<String> loadAboutContent(String languageCode) async {
    final String text = await rootBundle.loadString('assets/data/about_$languageCode.md');
    return text;
  }
}
