import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/faq_item.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';

class FAQScreen extends StatefulWidget {
  const FAQScreen({super.key});

  @override
  State<StatefulWidget> createState() => _FAQScreenState();
}

class _FAQScreenState extends State<FAQScreen> {
  @override
  Widget build(BuildContext context) {
    String languageCode = Localizations.localeOf(context).languageCode;
    return BackgroundScaffold(
      title: "FAQ",
      child: FutureBuilder(
        future: loadFAQItems(languageCode),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return const Center(child: Text('Loading Faield'));
          }
          final faqItems = snapshot.data ?? [];

          return ListView.builder(
            itemCount: faqItems.length,
            itemBuilder: (context, index) {
              final item = faqItems[index];
              return Container(
                margin: EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 4.0,
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: 4.0,
                  vertical: 4.0,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(16)),
                  color: Colors.white,
                ),
                child: Theme(
                    data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
                    child: ExpansionTile(
                      title: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Image.asset(R.assetsImagesIcFaqQuestion),
                          SizedBox(width: 8),
                          Expanded(child: Text(
                            item.question,
                            style: const TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                              color: primaryTextColor,
                            ),
                          ))
                        ],
                      ),
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Image.asset(R.assetsImagesIcFaqAnswer),
                              SizedBox(width: 8),
                              Expanded(child: Text(
                                item.answer,
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: secondaryTextColor,
                                ),
                              ))
                            ],
                          ),
                        ),
                      ],
                    )
                ),
              );
            },
          );
        },
      ),
    );
  }

  Future<List<FAQItem>> loadFAQItems(String languageCode) async {
    Logger().d("languageCode:$languageCode");
    final String jsonString = await rootBundle.loadString(
      'assets/data/faq_$languageCode.json',
    );
    final List<dynamic> jsonList = json.decode(jsonString);
    return jsonList.map((json) => FAQItem.fromJson(json)).toList();
  }
}
