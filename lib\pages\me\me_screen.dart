import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/pages/favorite/favorite_screen.dart';
import 'package:milestone/pages/guide/guide_step_container_screen.dart';
import 'package:milestone/pages/login/login_container.dart';
import 'package:milestone/pages/member/member_introduction_screen.dart';
import 'package:milestone/pages/member/member_single_level.dart';
import 'package:milestone/pages/orders/my_order_screen.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/profile_listener_mixin.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/image_widget.dart';
import 'package:milestone/widget/top_rounded_container_with_inner_border.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../main.dart';
import '../../utils/navigation_route.dart';
import '../../utils/whatsapp_launcher.dart';
import '../../widget/input_invite_code_dialog.dart';
import '../../widget/member_level_badge_view.dart';
import '../group/group_screen.dart';
import '../member/member_level_status_screen.dart';
import '../share/share_poster_view.dart';
import 'level_up_description_view.dart';
import 'levelup_view.dart';
import 'me_setting_screen.dart';

class MeScreen extends StatefulWidget {
  const MeScreen({super.key});

  @override
  State<MeScreen> createState() => _MeScreenState();
}

class _MeScreenState extends State<MeScreen> with ProfileListenerMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(
              Assets.imagesBgCommon,
              fit: BoxFit.cover,
              width: MediaQuery.of(context).size.width,
            ),
          ),
          Column(
            children: [
              buildAppBar(context),
              SizedBox(height: 8),
              GestureDetector(
                onTap: () {
                  if (isLogin()) {
                    customRouter(context, SharePosterView());
                  } else {
                    customRouter(context, LoginContainer());
                  }
                },
                child: buildInviteView(context),
              ),
              Expanded(child: buildBody(context)),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildInviteView(BuildContext context) {
    return Stack(
      children: [
        Center(
          child: Image.asset(
            R.assetsImagesBgMeInvite,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width - 24,
          ),
        ),

        Container(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 30),
          child: Row(
            children: [
              Image.asset(R.assetsImagesIcInviteLeft),
              SizedBox(width: 3),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    S.of(context).invite_and_eran_bonus,
                    style: TextStyle(
                      color: Color(0xFFFFE9C7),
                      fontWeight: FontWeight.w400,
                      fontSize: 13,
                    ),
                  ),
                  Row(
                    children: [
                      Text(
                        "${S.of(context).invite_code}: $inviteCode",
                        style: TextStyle(
                          color: Color(0xFFFFE9C7),
                          fontWeight: FontWeight.w400,
                          fontSize: 13,
                        ),
                      ),
                      SizedBox(width: 12),
                      InkWell(
                        onTap: () async {
                          await Clipboard.setData(
                            ClipboardData(text: inviteCode),
                          );
                          makeToast(S.of(context).copy_success);
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: 2,
                            horizontal: 8,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Color(0xFFE6AC44), Color(0xFFE5B670)],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: BorderRadius.all(Radius.circular(30)),
                          ),
                          child: Text(
                            S.of(context).home_search_instructions_copy,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w400,
                              fontSize: 13,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Spacer(),
              Image.asset(R.assetsImagesIcMeArrowLeft, color: Colors.white),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildInstructionView(BuildContext context) {
    return Stack(
      children: [
        Center(
          child: Image.asset(
            R.assetsImagesBgMeUsage,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width - 24,
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(vertical: 20, horizontal: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    S.of(context).usage_guideline_title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: primaryTextColor,
                    ),
                  ),
                  Image.asset(R.assetsImagesIcMeArrowLeft),
                ],
              ),
              SizedBox(height: 6),
              Text(
                S.of(context).usage_hint,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w400,
                  color: primaryTextColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildBody(BuildContext context) {
    return TopRoundedContainerWithInnerBorder(
      cornerRadius: 16,
      borderWidth: 1,
      borderColor: Colors.white,
      backgroundColor: backgroundColor,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      margin: EdgeInsets.zero,
      width: MediaQuery.of(context).size.width,
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: 8),
            memberLevelValue > 0
                ? InkWell(
                    onTap: () {
                      customRouter(
                        context,
                        LevelUpDescriptionView(
                          memberSingleLevel: memberSingleLevel,
                        ),
                      );
                    },
                    child: LevelUpView(
                      memberSingleLevel: memberSingleLevel,
                      step: integral,
                    ),
                  )
                : Container(),
            SizedBox(height: 8),
            InkWell(
              onTap: () {
                if (isLogin()) {
                  customRouter(context, GroupScreen());
                } else {
                  customRouter(context, LoginContainer());
                }
              },
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    Image.asset(R.assetsImagesIcMyTeam),
                    SizedBox(width: 4),
                    Text(
                      S.of(context).my_team,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Spacer(),
                    Image.asset(
                      R.assetsImagesIcMeArrowLeft,
                      color: secondaryTextColor,
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            spreadUid == null || spreadUid! <= 0 ? InkWell(
              onTap: () {
                if (isLogin()) {
                  showBottomInputInviteCodeView(context, onButtonTapped: () {
                    setState(() {
                      spreadUid = null;
                      Global.loadingGlobalInformation();
                    });
                  });
                } else {
                  customRouter(context, LoginContainer());
                }
              },
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    Image.asset(R.assetsImagesIcMeInviteCode),
                    SizedBox(width: 4),
                    Text(
                      S.of(context).input_invite_code,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Spacer(),
                    Image.asset(
                      R.assetsImagesIcMeArrowLeft,
                      color: secondaryTextColor,
                    ),
                  ],
                ),
              ),
            ): InkWell(
              onTap: () {
                String? phoneAccount = UserManager.getFullUserInfo()?.spreadPhone;
                if(phoneAccount != null) {
                  _makePhoneCall(phoneAccount);
                }
              },
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    Image.asset(Assets.imagesIcCallPhone, width: 16,),
                    SizedBox(width: 4),
                    Text(
                      S.of(context).contact_up,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Spacer(),
                    Text(
                      UserManager.getFullUserInfo()?.spreadNickName ?? "",
                      style: TextStyle(
                        color: Color(0xFF98938F),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(width: 4),
                    Image.asset(
                      R.assetsImagesIcMeArrowLeft,
                      color: secondaryTextColor,
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            InkWell(
              onTap: () {
                if (isLogin()) {
                  customRouter(context, MyOrderScreen());
                } else {
                  customRouter(context, LoginContainer());
                }
              },
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    Image.asset(R.assetsImagesIcMeOrders),
                    SizedBox(width: 4),
                    Text(
                      S.of(context).income_my_order,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Spacer(),
                    Image.asset(
                      R.assetsImagesIcMeArrowLeft,
                      color: secondaryTextColor,
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            InkWell(
              onTap: () {
                if (isLogin()) {
                  customRouter(context, FavoriteScreen());
                } else {
                  customRouter(context, LoginContainer());
                }
              },
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    Image.asset(R.assetsImagesIcMeStar),
                    SizedBox(width: 4),
                    Text(
                      S.of(context).my_collection,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Spacer(),
                    Image.asset(
                      R.assetsImagesIcMeArrowLeft,
                      color: secondaryTextColor,
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            GestureDetector(
              onTap: () {
                customRouter(context, GuideStepContainerScreen());
              },
              child: buildInstructionView(context),
            ),
          ],
        ),
      ),
    );
  }

  Padding buildAppBar(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      child: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: false,
        leading: ClipOval(
          child: ImageWidget(
            width: 52,
            height: 52,
            url: avatarUrl,
            defaultImagePath: Assets.imagesIcAvatarDefault,
            loadingWidth: 52,
            loadingHeight: 52,
          ),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              nickname,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: primaryTextColor,
              ),
            ),
            SizedBox(height: 4),
            InkWell(
              onTap: () {
                if (!Global.isDebugMode) {
                  if (memberLevelValue == 0) {
                    customRouter(
                      context,
                      MemberIntroductionScreen(showAll: true),
                    );
                  } else {
                    customRouter(
                      context,
                      MemberLevelStatusScreen(memberLevel: memberLevel),
                    );
                  }
                }
              },
              child: MemberLevelBadgeView(memberLevel: memberLevelValue),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              _launchWhatsApp();
            },
            child: Image.asset(R.assetsImagesIcCustomerService),
          ),
          TextButton(
            onPressed: () {
              if (isLogin()) {
                customRouter(context, MeSettingScreen());
              } else {
                customRouter(context, LoginContainer());
              }
            },
            child: Image.asset(R.assetsImagesIcConfig),
          ),
        ],
      ),
    );
  }

  final String whatsappNumber = "6281367496334";
  final String message = "Hello, I need help";

  Future<void> _launchWhatsApp() async {
    final url = "https://wa.me/$whatsappNumber?text=${Uri.encodeFull(message)}";
    final webUrl = "whatsapp://send?phone=$whatsappNumber&text=$message";

    if (await canLaunchUrl(Uri.parse(webUrl))) {
      await launchUrl(Uri.parse(webUrl));
    } else if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );

    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      throw 'Cannot call: $phoneNumber';
    }
  }
}
