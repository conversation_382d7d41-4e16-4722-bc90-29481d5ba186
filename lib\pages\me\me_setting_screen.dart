import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/pages/me/about_screen.dart';
import 'package:milestone/pages/me/faq_screen.dart';
import 'package:milestone/pages/me/modify_nickname_screen.dart';
import 'package:milestone/pages/me/privacy_screen.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/profile_listener_mixin.dart';
import 'package:milestone/utils/string.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:milestone/widget/image_widget.dart';

import '../../network/errors.dart';
import '../../utils/navigation_route.dart';
import '../../utils/toast_utils.dart';
import '../../widget/common_dialog.dart';
import '../../widget/loading_dialog.dart';
import '../login/bind_account_and_password_screen.dart';
import '../login/login_container.dart';
import '../login/verification_code_screen.dart';
import '../main_screen.dart';

class MeSettingScreen extends StatefulWidget {
  const MeSettingScreen({super.key});

  @override
  State<StatefulWidget> createState() => _MeSettingScreenState();
}

class _MeSettingScreenState extends State<MeSettingScreen> with ProfileListenerMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Image.asset(Assets.imagesIcArrowBack),
        ),
        title: Text(
          S.of(context).setting,
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: primaryTextColor,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            buildCommonAreaView(context),
            buildQuestionView(context),
            buildOtherView(context),
            buildLogoutView(context),
          ],
        ),
      ),
    );
  }

  Future<void> _performLogout(BuildContext context) async {
    try {
      showLoadingDialog();
      await UserManager.clearUserInfo();
      await networkApiClient.logout();
      dismissLoadingDialog();
      if (context.mounted) {
        customRouter(context, LoginContainer());
      }
    } catch (e) {
      dismissLoadingDialog();
      if (context.mounted) {
        ApiErrorHandler.handleError(e, context);
      }
    }
  }

  Widget buildLogoutView(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: InkWell(
        onTap: () async {
          showCustomDialog(
              context: context,
              title: S.of(context).logout_confirm_title,
              description: S.of(context).logout_confirm_message,
              confirmButtonText: S.of(context).confirm,
              cancelButtonText: S.of(context).cancel,
              onConfirm: (BuildContext dialogContext) async {
                await _performLogout(context);
              },
              onCancel: (BuildContext dialogContext) {
              }
          );
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              S.of(context).logout,
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildCommonAreaView(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () async {
              // try {
              //   final XFile? image = await ImagePicker().pickImage(
              //     source: ImageSource.gallery,
              //   );
              //   if (image == null) return;
              //   showLoadingDialog();
              //   Map<String, dynamic> result = await networkApiClient.uploadAvatar(image.path);
              //   Logger().d("Upload avatar result: $result");
              //   dismissLoadingDialog();
              // } catch (e) {
              //   if(context.mounted) {
              //     ApiErrorHandler.handleError(e, context);
              //     dismissLoadingDialog();
              //   }
              // }
              // TODO: refresh
            },
            child: buildAvatarView(context),
          ),
          Divider(color: backgroundColor),
          InkWell(
            onTap: () async {
              customRouter(context,  ModifyNicknameScreen());
            },
            child: buildNicknameView(context),
          ),
          Divider(color: backgroundColor),
          InkWell(onTap: () async {}, child: buildWhatsappView(context)),
          // Divider(color: backgroundColor),
          // InkWell(onTap: () async {
          // }, child: buildModifyPhoneView(context)),
          // Divider(color: backgroundColor),
          // InkWell(onTap: () async {
          //   if(UserManager.getPhone() != null) {
          //     String phoneNumber = UserManager.getPhone()!;
          //     showLoadingDialog(context);
          //     Response response = await networkApiClient
          //         .sendVerificationCode(phoneNumber);
          //     if (!context.mounted) return;
          //     Navigator.of(context).pop();
          //     if (response.statusCode == 200) {
          //       makeToast(S.of(context).login_code_sent);
          //       Navigator.of(context).push(
          //         MaterialPageRoute(
          //           builder: (context) =>
          //               VerificationCodeScreen(phoneNumber: phoneNumber, needUpdatePassword: true),
          //         ),
          //       );
          //     } else {
          //       makeToast(S.of(context).login_send_failed);
          //     }
          //   }
          // }, child: buildModifyPasswordView(context)),
        ],
      ),
    );
  }

  Widget buildAvatarView(BuildContext context) {
    return SizedBox(
      height: 44,
      child: Row(
        children: [
          Image.asset(R.assetsImagesIcUserAvatar),
          SizedBox(width: 6),
          Text(
            S.of(context).my_avatar,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          Spacer(),
          ClipOval(
            child: ImageWidget(
              width: 34,
              height: 34,
              url: "avatar",
              defaultImagePath: Assets.imagesIcAvatarDefault,
              loadingWidth: 34,
              loadingHeight: 34,
            ),
          ),
          SizedBox(width: 6),
          Image.asset(R.assetsImagesIcIncomeArrowRight),
        ],
      ),
    );
  }

  Widget buildNicknameView(BuildContext context) {
    return SizedBox(
      height: 44,
      child: Row(
        children: [
          Image.asset(R.assetsImagesIcMeSettingNickname),
          SizedBox(width: 6),
          Text(
            S.of(context).nickname,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          Spacer(),
          Text(
            nickname,
            style: TextStyle(
              color: secondaryTextColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(width: 6),
          Image.asset(R.assetsImagesIcIncomeArrowRight),
        ],
      ),
    );
  }

  Widget buildWhatsappView(BuildContext context) {
    return SizedBox(
      height: 44,
      child: Row(
        children: [
          Image.asset(R.assetsImagesIcMeSettingPhone),
          SizedBox(width: 6),
          Text(
            S.of(context).phone_number,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          Spacer(),
          Text(
            UserManager.getPhone()?.toMaskedBankCard() ?? "----",
            style: TextStyle(
              color: secondaryTextColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(width: 6),
          Image.asset(R.assetsImagesIcIncomeArrowRight),
        ],
      ),
    );
  }

  Widget buildModifyPhoneView(BuildContext context) {
    return SizedBox(
      height: 44,
      child: Row(
        children: [
          Image.asset(R.assetsImagesIcMeSettingModifyPhone),
          SizedBox(width: 6),
          Text(
            S.of(context).modify_phone_number,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          Spacer(),
          Text(
            UserManager.getPhone()?.toMaskedBankCard() ?? "----",
            style: TextStyle(
              color: secondaryTextColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(width: 6),
          Image.asset(R.assetsImagesIcIncomeArrowRight),
        ],
      ),
    );
  }

  Widget buildModifyPasswordView(BuildContext context) {
    return SizedBox(
      height: 44,
      child: Row(
        children: [
          Image.asset(R.assetsImagesIcMeSettingChangePassword),
          SizedBox(width: 6),
          Text(
            S.of(context).modify_password,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          Spacer(),
          Image.asset(R.assetsImagesIcIncomeArrowRight),
        ],
      ),
    );
  }

  Widget buildQuestionView(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: InkWell(
        onTap: () {
          customRouter(context, FAQScreen());
        },
        child: Row(
          children: [
            Image.asset(R.assetsImagesIcMeSettingFaq),
            SizedBox(width: 6),
            Text(
              "FAQ",
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
            Spacer(),
            Image.asset(R.assetsImagesIcIncomeArrowRight),
          ],
        ),
      ),
    );
  }

  Widget buildOtherView(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              customRouter(context, PrivacyScreen());
            },
            child: SizedBox(
              height: 44,
              child: Row(
                children: [
                  Image.asset(R.assetsImagesIcMeSettingPrivacy),
                  SizedBox(width: 6),
                  Text(
                    S.of(context).privacy,
                    style: TextStyle(
                      color: primaryTextColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Spacer(),
                  Image.asset(R.assetsImagesIcIncomeArrowRight),
                ],
              ),
            ),
          ),

          Divider(color: backgroundColor),

          InkWell(
            onTap: () {
              customRouter(context, AboutScreen());
            },
            child: SizedBox(
              height: 44,
              child: Row(
                children: [
                  Image.asset(R.assetsImagesIcMeSettingAbout),
                  SizedBox(width: 6),
                  Text(
                    S.of(context).about,
                    style: TextStyle(
                      color: primaryTextColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Spacer(),
                  Image.asset(R.assetsImagesIcIncomeArrowRight),
                ],
              ),
            ),
          ),

          Divider(color: backgroundColor),

          InkWell(
            onTap: () {
              showCustomDialog(
                context: context,
                title: S.of(context).delete_account_title,
                description: S.of(context).delete_account_content,
                confirmButtonText: S.of(context).confirm,
                cancelButtonText: S.of(context).cancel,
                onConfirm: (BuildContext dialogContext) async {
                  await _performLogout(context);
                },
                onCancel: (BuildContext dialogContext) {
                  // Navigator.pop(dialogContext);
                }
              );
            },
            child: SizedBox(
              height: 44,
              child: Row(
                children: [
                  Image.asset(R.assetsImagesIcDeleteAccount),
                  SizedBox(width: 6),
                  Text(
                    S.of(context).delete_account,
                    style: TextStyle(
                      color: primaryTextColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Spacer(),
                  Image.asset(R.assetsImagesIcIncomeArrowRight),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
