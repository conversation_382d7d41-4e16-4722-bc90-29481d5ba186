import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';

class PrivacyScreen extends StatelessWidget {
  const PrivacyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).privacy,
      child: Column(
        children: [
          Stack(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Image.asset(
                  R.assetsImagesBgPrivacy,
                  fit: BoxFit.cover,
                  width: MediaQuery.of(context).size.width - 24,
                ),
              ),
              Positioned(
                top: 45,
                left: 30,
                child: Text(
                  "Reminder terms:",
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 18,
                    color: primaryTextColor,
                  ),
                ),
              ),
            ],
          ),
          FutureBuilder(
            future: loadPrivacy(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }
              if (snapshot.hasError) {
                return Expanded(
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 12),
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    color: Colors.white,
                    child: Text('Loading Failed: ${snapshot.error.toString()}'),
                  ),
                );
              }
              String text = snapshot.data ?? "";
              return Expanded(
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 12),
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                      bottom: Radius.circular(22),
                    ),
                  ),

                  child: SingleChildScrollView(
                    child: Text(
                      text,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                        color: secondaryTextColor,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Future<String> loadPrivacy() async {
    final String privacy = await rootBundle.loadString(
      'assets/data/privacy.txt',
    );
    return privacy;
  }
}
