import 'package:flutter/material.dart';

class AnimatedListItem extends StatelessWidget {
  final int index;
  final Animation<double> animation;
  final Widget child;

  const AnimatedListItem({
    super.key,
    required this.index,
    required this.animation,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    // 计算延迟时间，创建交错效果
    final delay = index * 100; // 每个列表项延迟100ms

    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        // 创建交错动画
        final delayedAnimation = Tween<double>(
          begin: 0,
          end: 1,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: Interval(
              delay / 1000, // 转换为秒
              1.0,
              curve: Curves.easeOut,
            ),
          ),
        );

        return Transform.translate(
          offset: Offset(0, (1 - delayedAnimation.value) * 20),
          child: Opacity(
            opacity: delayedAnimation.value,
            child: child,
          ),
        );
      },
      child: child,
    );
  }
}