import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/main.dart';
import 'package:milestone/pages/member/member_single_level.dart';
import 'package:milestone/themes/colors.dart';
import 'package:step_progress/step_progress.dart';

import '../../generated/assets.dart';
import '../../generated/l10n.dart';
import '../../r.dart';
import 'animated_list_item.dart';
import 'member_level.dart';
import 'member_level_view.dart';

class MemberLevelStatusScreen extends StatefulWidget {
  final MemberLevel memberLevel;
  const MemberLevelStatusScreen({super.key, required this.memberLevel});

  @override
  State<StatefulWidget> createState() => MemberLevelStatusScreenState();
}

class MemberLevelStatusScreenState extends State<MemberLevelStatusScreen>
    with TickerProviderStateMixin {
  late List<MemberSingleLevel> memberSingleLevels;
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    Global.loadingGlobalInformation(complete: (){
      setState(() {

      });
    });
    memberSingleLevels = widget.memberLevel.singleLevels();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: widget.memberLevel.backgroundColor(),
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.light,
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Image.asset(Assets.imagesIcArrowBack, color: Colors.white),
        ),
        title: Text(
          S.of(context).member_level_state,
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
      body: Stack(
        children: [
          Image.asset(
            Assets.imagesBgMemberLevelStatus,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width,
          ),
          SafeArea(child: buildContentView(context)),
        ],
      ),
    );
  }

  Widget buildContentView(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        buildSliderCardsView(context),
        buildTopStepView(context),
        buildStepNameView(context),
        SizedBox(height: 20),
        buildBenefitContent(context),
      ],
    );
  }

  Widget buildBenefitContent(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: MediaQuery.of(context).size.width,
          height: 400,
          decoration: BoxDecoration(
            color: widget.memberLevel.backgroundColor(),
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        Image.asset(
          widget.memberLevel.backgroundImage(),
          width: MediaQuery.of(context).size.width,
          fit: BoxFit.cover,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 25),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(Assets.imagesIcMemberLevelLeft),
                SizedBox(width: 12),
                Text(
                  S.of(context).exclusive_benefits,
                  style: TextStyle(
                    color: widget.memberLevel.textColor(),
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(width: 12),
                Image.asset(Assets.imagesIcMemberLevelRight),
              ],
            ),
            SizedBox(height: 15),
            buildMemberBenefitsList(context),
          ],
        ),
      ],
    );
  }

  Widget buildMemberBenefitsList(BuildContext context) {
    MemberSingleLevel currentLevel = memberSingleLevels[currentIndex];
    List<MemberBenefits> benefits = currentLevel.memberBenefitsList(context);

    return ListView.builder(
      key: ValueKey(currentLevel.index),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: benefits.length,
      itemBuilder: (context, index) {
        final benefit = benefits[index];
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          margin: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(15),
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: currentLevel.backgroundColors(),
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Center(child: Image.asset(benefit.imageIcon)),
              ),
              const SizedBox(width: 14),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      benefit.title,
                      style: TextStyle(
                        color: widget.memberLevel.textColor(),
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      benefit.description,
                      style: TextStyle(
                        color: widget.memberLevel.descriptionTextColor(),
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget buildTopStepView(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 0, horizontal: 20),
      child: Stack(
        children: [
          Divider(height: 22, thickness: 1, color: Colors.white),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: memberSingleLevels.map((value) {
              MemberSingleLevel currentLevel =
                  memberSingleLevels[currentIndex];
              if (currentLevel == value) {
                return Image.asset(Assets.imagesIcMemberLevelStep);
              } else {
                return Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(3),
                  ),
                );
              }
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget buildStepNameView(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 0, horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: memberSingleLevels.map((value) {
          MemberSingleLevel currentLevel =
              memberSingleLevels[currentIndex];
          Color color = currentLevel == value
              ? Colors.white
              : Colors.white.withValues(alpha: 0.8);
          return Text(
            value.fullDisplayName(context),
            style: TextStyle(
              fontWeight: FontWeight.w400,
              color: color,
              fontSize: 12,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget buildSliderCardsView(BuildContext context) {
    return CarouselSlider(
      options: CarouselOptions(
        autoPlay: false,
        aspectRatio: 2.0,
        enlargeCenterPage: true,
        enableInfiniteScroll: false,
        onPageChanged: (index, reason) {
          setState(() {
            currentIndex = index;
          });
        },
      ),
      items: memberSingleLevels
          .map((value) => MemberLevelView(memberSingleLevel: value))
          .toList(),
    );
  }
}
