import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/pages/member/payment_finish_screen.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/loading_dialog.dart';
import 'package:milestone/widget/showQRcodeDialog.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../generated/assets.dart';
import '../../generated/l10n.dart';
import '../../models/create_recharge_response.dart';
import '../../models/payment_models.dart';
import '../../models/payment_response.dart';
import '../../network/payment_api_client.dart';
import '../../r.dart';
import '../../themes/colors.dart';
import '../../utils/navigation_route.dart';
import '../../utils/string.dart';
import '../../utils/toast_utils.dart';
import '../../widget/common_dialog.dart';
import '../../widget/dashed_divider.dart';
import '../../widget/round_check_box.dart';
import '../me/term_of_payment_screen.dart';
import 'member_level.dart';

class MemberOrderPaymentScreen extends StatefulWidget {
  final MemberLevel memberLevel;
  const MemberOrderPaymentScreen({super.key, required this.memberLevel});

  @override
  State<StatefulWidget> createState() => MemberOrderPaymentScreenState();
}

enum PaymentMethod { QrCode, OVO, GoPay, ShopeePay, DANA, AstraPay }

extension PaymentMethodValues on PaymentMethod {
  String imageIcon() {
    switch (this) {
      case PaymentMethod.QrCode:
        return R.assetsImagesIcQrcode;
      case PaymentMethod.OVO:
        return R.assetsImagesIcOvoPay;
      case PaymentMethod.GoPay:
        return R.assetsImagesIcGoPay;
      case PaymentMethod.ShopeePay:
        return R.assetsImagesIcShopeePay;
      case PaymentMethod.DANA:
        return R.assetsImagesIcDanaPay;
      case PaymentMethod.AstraPay:
        return R.assetsImagesIcAstrapay;
    }
  }
}

class MemberOrderPaymentScreenState extends State<MemberOrderPaymentScreen> {
  bool isChecked = false;
  List<PaymentMethod> paymentMethodList = PaymentMethod.values;
  List<PaymentMethod> eWalletPaymentList = [
    PaymentMethod.OVO,
    PaymentMethod.GoPay,
    PaymentMethod.ShopeePay,
    PaymentMethod.DANA,
    PaymentMethod.AstraPay,
  ];
  PaymentMethod currentPayment = PaymentMethod.QrCode;

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).order_payment,
      backgroundColor: backgroundColor,
      bottomNavigationBar: Container(
        margin: EdgeInsets.only(
          right: 16,
          left: 16,
          top: 20,
          bottom: 20 + MediaQuery.of(context).padding.bottom,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [becomeMemberButton(context), buildCheckboxView(context)],
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildTicketInformationView(context),
            SizedBox(height: 8),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                S.of(context).please_choose_payment_method,
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 14,
                  color: primaryTextColor,
                ),
              ),
            ),
            SizedBox(height: 8),
            buildPaymentMethodWithQrCode(context),
            SizedBox(height: 8),
            buildPaymentMethodWithEWallet(context),
          ],
        ),
      ),
    );
  }

  Widget buildPaymentMethodWithEWallet(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
      ),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          title: Text(
            S.of(context).e_wallet,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
          children: eWalletPaymentList.map((payment) {
            return buildPaymentMethodWithEWalletItem(context, payment);
          }).toList(),
        ),
      ),
    );
  }

  Widget buildPaymentMethodWithEWalletItem(
    BuildContext context,
    PaymentMethod paymentMethod,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Row(
        children: [
          Image.asset(paymentMethod.imageIcon()),
          SizedBox(width: 4),
          Expanded(
            child: Text(
              paymentMethod.name,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 14,
                color: primaryTextColor,
              ),
            ),
          ),
          RoundCheckBox(
            isChecked: currentPayment == paymentMethod,
            size: 18,
            onTap: (selected) {
              setState(() {
                currentPayment = paymentMethod;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget buildPaymentMethodWithQrCode(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Image.asset(R.assetsImagesIcQrcode),
          SizedBox(width: 4),
          Expanded(
            child: Text(
              S.of(context).qrcode,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 14,
                color: primaryTextColor,
              ),
            ),
          ),
          RoundCheckBox(
            isChecked: currentPayment == PaymentMethod.QrCode,
            size: 18,
            onTap: (selected) {
              setState(() {
                currentPayment = PaymentMethod.QrCode;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget buildTicketInformationView(BuildContext context) {
    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: Image.asset(
            Assets.imagesBgPaymentOrder,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width - 40,
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Image.asset(Assets.imagesIcPaymentOrder, width: 47, height: 47),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    "Rp",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF5BC37E),
                    ),
                  ),
                  Text(
                    "${widget.memberLevel.fee()}",
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF5BC37E),
                    ),
                  ),
                  Text(
                    "/${S.of(context).year}",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF5BC37E),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 5),
              Text(
                S.of(context).order_price,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: secondaryTextColor,
                ),
              ),
              const SizedBox(height: 18),
              const DashedDivider(height: 1),
              const SizedBox(height: 18),
              _buildRow(S.of(context).product_name, S.of(context).agent_fee),
              _buildRow(
                S.of(context).real_payment_price,
                "${widget.memberLevel.fee()}",
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: secondaryTextColor)),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.right,
              style: const TextStyle(color: primaryTextColor),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildCheckboxView(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        RoundCheckBox(
          isChecked: isChecked,
          size: 14,
          onTap: (selected) {
            setState(() {
              isChecked = selected!;
            });
          },
        ),
        SizedBox(width: 6),
        Text(
          S.of(context).payment_agreement,
          style: TextStyle(
            color: secondaryTextColor,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
        InkWell(
          onTap: () {
            customRouter(context, TermOfServiceScreen());
          },
          child: Text(
            S.of(context).payment_agreement_link,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> startQRPayment() async {
    try {
      showLoadingDialog();
      CreateRechargeResponse rechargeResponse = await networkApiClient
          .createRecharge(widget.memberLevel.paymentValue(), "haipay", widget.memberLevel.fee());
      PaymentResponse paymentResponse = await networkApiClient.requestPayment(
        uni: "1",
        bizType: "2",
        orderNo: rechargeResponse.orderNo,
        payType: "QR",
        payChannel: "haipay",
        from: "app",
      );
      dismissLoadingDialog();
      showQRcodeDialog(
        context: context,
        urlLink: paymentResponse.payUrl ?? "",
        onConfirm: (BuildContext dialogContext) async {
          final Uri uri = Uri.parse(paymentResponse.payUrl ?? "");
          if (!await launchUrl(uri, mode: LaunchMode.platformDefault) &&
              context.mounted) {
            makeToast(S.of(context).can_not_open_link);
          }
          Navigator.of(dialogContext).pop();
          showFinishPaymentDialog(rechargeResponse);
        },
        onCancel: (BuildContext dialogContext) async {
          showFinishPaymentDialog(rechargeResponse);
        },
      );
    } catch (e) {
      dismissLoadingDialog();
      Logger().d(e.toString());
      makeToast(e.toString());
    }
  }

  void showFinishPaymentDialog(CreateRechargeResponse paymentResponse) {
    showCustomDialog(
      context: context,
      title: "Rp${widget.memberLevel.fee()}/${S.of(context).year}",
      description: S.of(context).order_price,
      confirmButtonText: S.of(context).payment_complete,
      cancelButtonText: S.of(context).payment_problem,
      onConfirm: (BuildContext dialogContext) async {
        customRouter(
          context,
          PaymentFinishScreen(
            memberLevel: widget.memberLevel,
            paymentResponse: paymentResponse,
          ),
        );
      },
      onCancel: (BuildContext dialogContext) {
        // Navigator.pop(dialogContext);
      },
    );
  }

  Map<String, CreatePayResponse> cachedPayResponse = {};

  Future<void> startEWalletPayment() async {
    try {
      showLoadingDialog();
      CreateRechargeResponse rechargeResponse = await networkApiClient
          .createRecharge(widget.memberLevel.paymentValue(), "xendit", widget.memberLevel.fee());
      CreatePayResponse? payResponse = cachedPayResponse[rechargeResponse.orderNo];
      if (payResponse == null) {
        int userId = UserManager.getUserId() ?? 0;
        String channelCode = currentPayment.name.toUpperCase();
        payResponse = await paymentApiClient.createPay(
            payType: "EWALLET",
            type: "EWALLET",
            reusability: "ONE_TIME_USE",
            referenceId: rechargeResponse.orderNo,
            givenNames: "$userId",
            surname: "$userId",
            channelCode: channelCode,
            amount: rechargeResponse.amount,
            metadata: rechargeResponse.toMap()
        );
        cachedPayResponse[rechargeResponse.orderNo] = payResponse;
      }
      dismissLoadingDialog();

      showQRcodeDialog(
        context: context,
        urlLink: payResponse.data.actions.first.url ?? "",
        onConfirm: (BuildContext dialogContext) async {
          final Uri uri = Uri.parse(payResponse?.data.actions.first.url ?? "");
          if (!await launchUrl(uri, mode: LaunchMode.platformDefault) &&
              context.mounted) {
            makeToast(S.of(context).can_not_open_link);
          }
          Navigator.of(dialogContext).pop();
          showFinishPaymentDialog(rechargeResponse);
        },
        onCancel: (BuildContext dialogContext) async {
          showFinishPaymentDialog(rechargeResponse);
        },
      );
    } catch (e) {
      dismissLoadingDialog();
      Logger().d(e.toString());
      makeToast(e.toString());
    }
  }

  Future<void> performPayment() async {
    if (currentPayment == PaymentMethod.QrCode) {
      await startQRPayment();
    } else if (eWalletPaymentList.contains(currentPayment)) {
      startEWalletPayment();
    }
  }

  Widget becomeMemberButton(BuildContext context) {
    return TextButton(
      onPressed: () async {
        if (!isChecked) {
          showCustomDialog(
            context: context,
            title: S.of(context).agree_with_payment_term,
            description: S.of(context).agree_with_payment_term,
            confirmButtonText: S.of(context).confirm,
            cancelButtonText: S.of(context).cancel,
            onConfirm: (BuildContext dialogContext) async {
              setState(() {
                isChecked = true;
              });
              await performPayment();
            },
            onCancel: (BuildContext dialogContext) {},
          );
          return;
        }
        await performPayment();
      },
      child: Container(
        height: 46,
        margin: EdgeInsets.only(right: 16, left: 16, top: 20, bottom: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFE6AC44), Color(0xFFE5B670)],
          ),
          borderRadius: BorderRadius.circular(30),
        ),
        child: Center(
          child: Text(
            S.of(context).purchase_right_now,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: fourthTextColor,
            ),
          ),
        ),
      ),
    );
  }
}
