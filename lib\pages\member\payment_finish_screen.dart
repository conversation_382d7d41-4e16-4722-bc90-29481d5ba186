import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/models/payment_response.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';

import '../../generated/l10n.dart';
import '../../models/create_recharge_response.dart';
import '../../models/payment_query_response.dart';
import '../../models/payment_status.dart';
import '../../utils/user_utils.dart';
import 'member_level.dart';

class PaymentFinishScreen extends StatefulWidget {
  final MemberLevel memberLevel;
  final CreateRechargeResponse paymentResponse;
  const PaymentFinishScreen({
    super.key,
    required this.memberLevel,
    required this.paymentResponse,
  });

  @override
  State<StatefulWidget> createState() => PaymentFinishScreenState();
}

class PaymentFinishScreenState extends State<PaymentFinishScreen> {
  PaymentStatus paymentStatus = PaymentStatus.paying;
  final int _maxRetries = 5; // 最多重试次数
  final Duration _retryDelay = Duration(seconds: 5); // 重试间隔
  int _retryCount = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      checkPaymentResult();
    });
  }

  Future<void> checkPaymentResult() async {
    try {
      // 调用API查询支付状态
      final PaymentQueryResponse response =
      await networkApiClient.queryPaymentOrderStatus(
        orderNo: widget.paymentResponse.orderNo,
      );

      if (response.paid) {
        networkApiClient.getUserInfo().then((userInfo) {
          Logger().d("userInfo: $userInfo");
          UserManager.saveFullUserInfo(userInfo);
        });
        setState(() {
          paymentStatus = PaymentStatus.success;
        });
      } else {
        // 支付未完成，检查重试次数
        if (_retryCount < _maxRetries) {
          _retryCount++;
          await Future.delayed(_retryDelay); // 等待5秒
          checkPaymentResult(); // 递归调用自身
        } else {
          // 超过最大重试次数仍未支付
          setState(() {
            paymentStatus = PaymentStatus.failed;
          });
        }
      }
    } catch (e) {
      // API调用异常处理
      Logger().e("支付状态查询失败: $e");

      if (_retryCount < _maxRetries) {
        _retryCount++;
        await Future.delayed(_retryDelay);
        checkPaymentResult();
      } else {
        setState(() {
          paymentStatus = PaymentStatus.failed;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).payment_complete,
      backgroundColor: backgroundColor,
      child: Column(
        children: [
          statusView(context),
          buildPaymentContent(context),
          paymentStatus == PaymentStatus.paying
              ? Container()
              : finishButton(context),
        ],
      ),
    );
  }

  Widget statusView(BuildContext context) {
    switch (paymentStatus) {
      case PaymentStatus.paying:
        return checkPaymentResultView(context);
      case PaymentStatus.success:
        return successResultView(context);
      case PaymentStatus.failed:
        return failedResultView(context);
    }
  }

  Widget finishButton(BuildContext context) {
    return TextButton(
      onPressed: () {
        Navigator.of(context).pop();
      },
      child: Container(
        height: 46,
        margin: EdgeInsets.only(right: 16, left: 16, top: 20, bottom: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFE6AC44), Color(0xFFE5B670)],
          ),
          borderRadius: BorderRadius.circular(30),
        ),
        child: Center(
          child: Text(
            S.of(context).finish,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: fourthTextColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget successResultView(BuildContext context) {
    return Column(
      children: [
        Image.asset(Assets.imagesIcPaymentSuccess),
        SizedBox(height: 12),
        Text(
          S.of(context).payment_success,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: primaryTextColor,
          ),
        ),
        SizedBox(height: 12),
      ],
    );
  }

  Widget failedResultView(BuildContext context) {
    return Column(
      children: [
        Image.asset(Assets.imagesIcPaymentFailed),
        SizedBox(height: 12),
        Text(
          S.of(context).payment_failed,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: primaryTextColor,
          ),
        ),
        SizedBox(height: 12),
      ],
    );
  }

  Widget checkPaymentResultView(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CupertinoActivityIndicator(),
        Text(
          S.of(context).check_payment_result,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: primaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget buildPaymentContent(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(15)),
      ),
      child: Column(
        children: [
          _buildRow(
            S.of(context).payment_amount,
            "Rp${widget.paymentResponse.amount}",
          ),
          _buildRow(
            S.of(context).product_name,
            widget.memberLevel.displayName(context),
          ),
          _buildRow(S.of(context).payment_id, widget.paymentResponse.orderNo),
          _buildRow(S.of(context).payment_method, "haipay"),
        ],
      ),
    );
  }

  Widget _buildRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: secondaryTextColor)),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.right,
              style: const TextStyle(color: primaryTextColor),
              maxLines: 3,
            ),
          ),
        ],
      ),
    );
  }
}
