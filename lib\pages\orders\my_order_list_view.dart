import 'package:flutter/material.dart';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/order_item.dart';
import 'package:milestone/models/order_product_info_model.dart';
import 'package:milestone/models/order_status.dart';
import 'package:milestone/widget/bottom_info_view.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/loading_more_indicator.dart';
import 'package:milestone/widget/empty_view.dart';

import '../../models/order_model.dart';
import 'my_order_loading_more.dart';

class MyOrderListView extends StatefulWidget {
  final OrderStatus orderStatus;
  const MyOrderListView({super.key, required this.orderStatus});

  @override
  State<MyOrderListView> createState() => _MyOrderListViewState();
}

class _MyOrderListViewState extends State<MyOrderListView> {
  late OrderLoadingMoreAdapter adapter;

  @override
  void initState() {
    super.initState();
    adapter = OrderLoadingMoreAdapter(orderStatus: widget.orderStatus);
  }

  @override
  Widget build(BuildContext context) {
    return LoadingMoreList<OrderModel>(
      ListConfig<OrderModel>(
        sourceList: adapter,
        itemBuilder: (context, item, index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: () {
                String rate;
                String cash;
                if (item.orderInfoList.length == 1) {
                  rate = item.orderInfoList.first.commissionRate ?? "";
                  cash = item.orderInfoList.first.actualCommission ?? "";
                } else {
                  // 多商品情况：计算平均返现率和总返现金额
                  double totalCommission = 0.0;   // 总返现金额
                  double totalRate = 0.0;         // 佣金率总和
                  int validRateCount = 0;          // 有效佣金率计数

                  for (OrderProductInfoModel productInfo in item.orderInfoList) {
                    // 处理返现金额
                    if (productInfo.actualCommission != null) {
                      try {
                        totalCommission += double.parse(productInfo.actualCommission!);
                      } catch (e) {
                        // 转换失败时跳过
                      }
                    }

                    // 处理佣金率（需处理带%符号的情况）
                    if (productInfo.commissionRate != null && productInfo.commissionRate!.isNotEmpty) {
                      try {
                        String rateStr = productInfo.commissionRate!;
                        // 移除可能存在的%符号
                        if (rateStr.contains('%')) {
                          rateStr = rateStr.replaceAll('%', '');
                        }
                        totalRate += double.parse(rateStr);
                        validRateCount++;
                      } catch (e) {
                        // 转换失败时跳过
                      }
                    }
                  }

                  // 计算平均返现率（保留两位小数）
                  double avgRate = validRateCount > 0 ? totalRate / validRateCount : 0.0;
                  rate = '${avgRate.toStringAsFixed(2)}%';

                  // 计算总返现金额（保留两位小数）
                  cash = totalCommission.toStringAsFixed(2);
                }

                showBottomInfoView(
                  context,
                  S.of(context).rebase_info,
                  S.of(context).rebase_price,
                  S.of(context).rebase_rate,
                  S.of(context).rebase_cash,
                  "Rp${item.payPrice}",
                  rate,
                  "Rp$cash",
                );
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        '${S.of(context).order_application_time}${item.createTime}',
                        style: TextStyle(fontSize: 12, color: primaryTextColor),
                      ),
                      const Spacer(),
                      Text(
                        item.orderStatus ?? "",
                        style: TextStyle(
                          fontSize: 12,
                          color: item.statusColor(),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  /// Middle: 商品图 + 标题
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          item.orderInfoList.first.image ?? "",
                          width: 76,
                          height: 76,
                          fit: BoxFit.cover,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          children: [
                            Text(
                              item.orderInfoList.first.storeName ?? "",
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: primaryTextColor,
                              ),
                            ),
                            SizedBox(height: 6),
                            Row(
                              children: [
                                Text(
                                  '${S.of(context).order_expected_cashback} Rp${item.orderInfoList.first.actualCommission}',
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: highlightTextColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(width: 2),
                                Image.asset(
                                  R.assetsImagesIcDetailQuestion,
                                  color: secondaryTextColor,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
        indicatorBuilder: (context, status) {
          switch (status) {
            case IndicatorStatus.none:
            case IndicatorStatus.loadingMoreBusying:
            case IndicatorStatus.fullScreenBusying:
              return buildLoadingMoreContent(context);
            case IndicatorStatus.noMoreLoad:
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Text(
                    S.of(context).loading_more_no_more,
                    style: const TextStyle(
                      color: Color(0xFFCCCCCC),
                      fontSize: 14,
                    ),
                  ),
                ),
              );
            case IndicatorStatus.empty:
              return const EmptyView();
            default:
              return const SizedBox();
          }
        },
      ),
    );
  }

  String _formatDateTime(DateTime time) {
    return '${time.year}年${time.month}月${time.day}日${time.hour}点${time.minute}分';
  }
}
