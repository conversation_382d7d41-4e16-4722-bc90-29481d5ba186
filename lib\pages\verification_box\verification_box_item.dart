import 'package:flutter/material.dart';
import 'package:milestone/themes/colors.dart';

///
/// 输入框样式
///
enum VerificationBoxItemType {
  ///
  ///下划线
  ///
  underline,

  ///
  /// 盒子
  ///
  box,
}

///
/// 单个输入框
///
class VerificationBoxItem extends StatelessWidget {
  const VerificationBoxItem(
      {super.key,
      this.data = '',
      required this.textStyle,
      this.type = VerificationBoxItemType.box,
      this.decoration,
      this.borderRadius = 8.0,
      this.borderWidth = 1.0,
      this.borderColor,
      this.showCursor = false,
      this.cursorColor,
      this.cursorWidth = 2,
      this.cursorIndent = 5,
      this.cursorEndIndent = 5});

  final String data;
  final VerificationBoxItemType type;
  final double borderWidth;
  final Color? borderColor;
  final double borderRadius;
  final TextStyle textStyle;
  final Decoration? decoration;

  ///
  /// 是否显示光标
  ///
  final bool showCursor;

  ///
  /// 光标颜色
  ///
  final Color? cursorColor;

  ///
  /// 光标宽度
  ///
  final double cursorWidth;

  ///
  /// 光标距离顶部距离
  ///
  final double cursorIndent;

  ///
  /// 光标距离底部距离
  ///
  final double cursorEndIndent;

  @override
  Widget build(BuildContext context) {
    var borderColor = this.borderColor ?? Color(0xFFFAF3F2);
    var backgroundColor = Colors.white;
    var shadowColor = Colors.white;
    var text = _buildText();
    Widget mWidget;
    if (type == VerificationBoxItemType.box) {
      mWidget =
          _buildBoxDecoration(text, borderColor, backgroundColor, shadowColor);
    } else {
      mWidget = _buildUnderlineDecoration(text, borderColor);
    }

    return Stack(
      children: <Widget>[
        mWidget,
        showCursor
            ? Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(borderRadius),
                    boxShadow: const [
                      BoxShadow(
                          color: Colors.black12,
                          offset: Offset(0.0, 1.0), //阴影xy轴偏移量
                          blurRadius: 10.0, //阴影模糊程度
                          spreadRadius: 0 //阴影扩散程度
                          )
                    ]),
              )
            : Container()
      ],
    );
  }

  ///
  /// 绘制盒子类型
  ///
  Container _buildBoxDecoration(Widget child, Color borderColor, Color backgroundColor,
      Color shadowColor) {
    return Container(
      width: 45,
      height: 45,
      alignment: Alignment.center,
      decoration: decoration ??
          BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: data.isEmpty
                  ? [
                      const BoxShadow(
                          color: Colors.black12,
                          offset: Offset(5, 1),
                          blurRadius: 25,
                          spreadRadius: 0.5),
                      BoxShadow(
                          color: Colors.white,
                          offset: -const Offset(5, 1),
                          blurRadius: 25,
                          spreadRadius: 0.5)
                    ]
                  : [
                      BoxShadow(
                          color: shadowColor,
                          offset: const Offset(0.0, 1.0), //阴影xy轴偏移量
                          blurRadius: 10.0, //阴影模糊程度
                          spreadRadius: 0.5 //阴影扩散程度
                          )
                    ]),
      child: child,
    );
  }

  ///
  /// 绘制下划线类型
  ///
  Container _buildUnderlineDecoration(Widget child, Color borderColor) {
    return Container(
      alignment: Alignment.center,
      decoration: UnderlineTabIndicator(
          borderSide: BorderSide(width: borderWidth, color: borderColor)),
      child: child,
    );
  }

  ///
  /// 文本
  ///
  Text _buildText() {
    return Text(
      data,
      style: textStyle,
    );
  }
}
