import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/pages/withdrawal/select_bank_list_screen.dart';
import 'package:milestone/pages/withdrawal/withdrawal_choose_screen.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/bottom_info_view.dart';

class WithdrawalAddBankCardScreen extends StatefulWidget {
  const WithdrawalAddBankCardScreen({super.key});

  @override
  State<WithdrawalAddBankCardScreen> createState() =>
      _WithdrawalAddBankCardScreenState();
}

class _WithdrawalAddBankCardScreenState
    extends State<WithdrawalAddBankCardScreen> {

  String? selectedBankName;
  String? name;
  String? bankCardNumber;

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).withdrawal_add_card,
      child: Column(
        children: [
          buildBankView(context),
          Spacer(),
          buildWithdrawalNextButton(context),
        ],
      ),
    );
  }

  Widget buildBankView(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).name,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
          SizedBox(height: 12),
          TextField(
            onChanged: (value) {
              setState(() {
                name = value;
              });
            },
            keyboardType: TextInputType.name,
            decoration: InputDecoration(
              hintText: S.of(context).name_placeholder,
              border: InputBorder.none,
              hintStyle: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: secondaryTextColor,
              ),
            ),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
          Divider(height: 24, thickness: 1, color: backgroundColor),
          InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SelectBankListScreen(),
                ),
              ).then((bankName) {
                setState(() {
                  selectedBankName = bankName;
                });
              });
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).bank_name,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: primaryTextColor,
                  ),
                ),
                SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: selectedBankName != null
                          ? Text(
                        selectedBankName!,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: primaryTextColor,
                        ),
                        softWrap: true,  // 确保启用换行（默认true可不写）
                        overflow: TextOverflow.visible, // 允许内容超出父组件
                      )
                          : Text(
                        S.of(context).select_bank,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: secondaryTextColor,
                        ),
                      ),
                    ),
                    SizedBox(width: 8), // 添加间距避免紧贴图标
                    Image.asset(Assets.imagesIcIncomeArrowRight),
                  ],
                ),
              ],
            ),
          ),
          Divider(height: 24, thickness: 1, color: backgroundColor),
          Text(
            S.of(context).bank_card_number,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
          SizedBox(height: 12),
          TextField(
            onChanged: (value) {
              setState(() {
                bankCardNumber = value;
              });
            },
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: S.of(context).input_bank_card_number,
              border: InputBorder.none,
              hintStyle: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: secondaryTextColor,
              ),
            ),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildWithdrawalNextButton(BuildContext context) {
    return TextButton(
        onPressed: () {
          if(name == null || name!.isEmpty) {
            makeToast(S.of(context).name_placeholder);
            return;
          }
          if(selectedBankName == null || selectedBankName!.isEmpty) {
            makeToast(S.of(context).please_select_bank);
            return;
          }

          if(bankCardNumber == null || bankCardNumber!.isEmpty) {
            makeToast(S.of(context).please_input_bank_number);
            return;
          }

          showBottomInfoView(
              context,
              S.of(context).bind_bank_card_confirm,
              S.of(context).name,
              S.of(context).bank_name,
              S.of(context).bank_card_number,
              name ?? "",
              selectedBankName ?? "",
              bankCardNumber ?? "",
              onButtonTapped: ()async  {
                Map<String, String> result = {
                  "name": name ?? "",
                  "bank_name": selectedBankName ?? "",
                  "bank_card_number": bankCardNumber ?? "",
                };

                Navigator.pop(context, result);
              }
          );
        },
        child: Container(
          width: MediaQuery.of(context).size.width - 32,
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: selectedTabColor,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              S.of(context).button_next,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        )
    );
  }
}
