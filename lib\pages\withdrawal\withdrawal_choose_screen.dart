import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/pages/withdrawal/withdrawal_add_bank_card_screen.dart';
import 'package:milestone/pages/withdrawal/withdrawal_add_e_wallet_screen.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';

import '../../utils/navigation_route.dart';

class WithdrawalChooseScreen extends StatefulWidget {
  const WithdrawalChooseScreen({super.key});

  @override
  State<WithdrawalChooseScreen> createState() => _WithdrawalChooseScreenState();
}

class _WithdrawalChooseScreenState extends State<WithdrawalChooseScreen> {
  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).withdrawal_choose_method,
      child: Column(
        children: [
          buildChooseView(
            context,
            R.assetsImagesIcWithdrawAddCard,
            S.of(context).withdrawal_add_card,
            () async {
              final result = await customRouter(context, WithdrawalAddBankCardScreen());
              result["type"] = "bank";
              Logger().d("result: $result");
              if(context.mounted) {
                Navigator.pop(context, result);
              }
            },
          ),
          buildChooseView(
            context,
            R.assetsImagesIcWithdrawEWallet,
            S.of(context).withdrawal_add_e_card,
            () async {
              final result = await customRouter(context, WithdrawalAddEWalletScreen());
              result["type"] = "e_wallet";
              Logger().d("result: $result");
              if(context.mounted) {
                Navigator.pop(context, result);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget buildChooseView(
    BuildContext context,
    String icon,
    String title,
    GestureTapCallback? onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width,
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(icon),
            SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: primaryTextColor,
              ),
            ),
            Spacer(),
            Image.asset(R.assetsImagesIcWithdrawAdd),
          ],
        ),
      ),
    );
  }
}
