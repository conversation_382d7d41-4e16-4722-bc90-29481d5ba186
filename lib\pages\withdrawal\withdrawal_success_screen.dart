import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/themes/colors.dart';

class WithdrawalSuccessScreen extends StatefulWidget {

  const WithdrawalSuccessScreen({super.key});

  @override
  State<WithdrawalSuccessScreen> createState() => _WithdrawalSuccessScreenState();
}

class _WithdrawalSuccessScreenState extends State<WithdrawalSuccessScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          Image.asset(
            Assets.imagesBgWithdrawalSuccess,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Column(
            children: [
              AppBar(
                elevation: 0,
                backgroundColor: Colors.transparent,
                centerTitle: true,
                leading: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Image.asset(Assets.imagesIcArrowBack),
                ),
                title: Text(
                  S.of(context).withdrawal_finish,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: primaryTextColor,
                  ),
                ),
              ),

              ClipRRect(
                borderRadius: BorderRadius.all(Radius.circular(37)),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                  child: Container(
                    margin: EdgeInsets.all(12),
                    padding: EdgeInsets.symmetric(vertical: 40, horizontal: 55),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.all(Radius.circular(37)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          margin: EdgeInsets.all(20),
                          child:Center(child:  Image.asset(Assets.imagesIcWithdrawalSuccess)),
                        ),
                        Text(
                          S.of(context).withdrawal_success,
                          style: TextStyle(
                            color: primaryTextColor,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 12),
                        Text(
                          S.of(context).withdrawal_success_hint,
                          style: TextStyle(
                            color: secondaryTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 20),
                        buildWithdrawalFinishButton(context)
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildWithdrawalFinishButton(BuildContext context) {
    return TextButton(
        onPressed: () {
          Navigator.pop(context);
        },
        child: Container(
          width: MediaQuery.of(context).size.width - 32,
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: selectedTabColor,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              S.of(context).finish,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        )
    );
  }
}