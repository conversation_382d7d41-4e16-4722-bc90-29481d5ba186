import 'package:flutter/material.dart';
import 'package:milestone/themes/colors.dart';
import 'package:toastification/toastification.dart';

void makeToast(String message) {
  if (message.isEmpty) return;
  toastification.show(
    title: Text(
      message,
      style: TextStyle(
        color: primaryTextColor,
        fontWeight: FontWeight.w400,
        fontSize: 14,
      ),
    ),
    showIcon: false,
    borderRadius: BorderRadius.circular(30),
    style: ToastificationStyle.simple,
    autoCloseDuration: const Duration(seconds: 2),
  );
}
