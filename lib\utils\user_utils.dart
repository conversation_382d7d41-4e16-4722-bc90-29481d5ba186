import 'package:logger/logger.dart';
import 'package:milestone/pages/member/member_level.dart';
import 'package:milestone/pages/member/member_single_level.dart';
import 'package:milestone/utils/sp.dart';
import 'package:get/get.dart';

import '../controller/event_bus_controller.dart';
import '../models/user_info.dart';

const String spToken = "SP_TOKEN";

bool isUrl(String text) {
  const urlPattern =
      r"(http|https)://([a-zA-Z0-9\-\._]+\/?|[a-zA-Z0-9\-\._]+\.[a-zA-Z]{2,}\/?)([a-zA-Z0-9\-\._\?\,\'/\\\+&%\$#\=~]*)";
  final result = RegExp(urlPattern, caseSensitive: false).hasMatch(text);
  return result;
}

bool isLogin() => UserManager.isLogin();

class UserManager {
  // 存储键名常量
  static const String spToken = "SP_TOKEN";
  static const String spUserInfo = "SP_USER_INFO";
  static const String spFullUserInfo = "SP_FULL_USER_INFO";

  // 保存用户信息（登录成功后调用）
  static Future<void> saveUserInfo(Map<String, dynamic> userInfo) async {
    if (userInfo.containsKey('token')) {
      await SpUtil.putString(spToken, userInfo['token'] as String);
    }
    eventBus.fire(LoginEvent(userInfo: userInfo));
    await SpUtil.putObject(spUserInfo, userInfo);
  }

  static Future<void> saveFullUserInfo(UserInfo userInfo) async {
    await SpUtil.putObject(spFullUserInfo, userInfo.toJson());
    eventBus.fire(ProfileUpdateEvent(
      username: userInfo.nickname
    ));
  }

  // 获取当前登录用户的token
  static String getToken() {
    return SpUtil.getString(spToken, defValue: "");
  }

  // 获取当前登录用户的完整信息
  static Map? getUserInfo() {
    return SpUtil.getObject(spUserInfo);
  }

  static String getAvatar() {
    final info = getUserInfo();
    return info?['avatar'] as String? ?? '';
  }

  // 获取用户ID
  static int? getUserId() {
    final info = getUserInfo();
    return info?['uid'] as int?;
  }

  // 获取用户昵称
  static String? getNickname() {
    final info = getUserInfo();
    return info?['nikeName'] as String?;
  }

  static int? spreadUid() {
    final info = getUserInfo();
    return info?['spreadUid'] as int?;
  }

  static Future<Map<dynamic, dynamic>?> updateUserInfo(String key, String value) async {
    final info = getUserInfo();
    Logger().d("updateUserInfo: $key = $value, current info: $info");

    // 同时更新基础信息和完整信息
    if (info != null) {
      info[key] = value;
      await SpUtil.putObject(spUserInfo, info);
    }

    final fullInfo = getFullUserInfo();
    if (fullInfo != null) {
      switch (key) {
        case 'nikeName':
          final updatedInfo = fullInfo.copyWith(nickname: value);
          await saveFullUserInfo(updatedInfo);
          break;
        case 'avatar':
          final updatedInfo = fullInfo.copyWith(avatar: value);
          await saveFullUserInfo(updatedInfo);
          break;
      }
    }

    if (key == "nikeName") {
      eventBus.fire(ProfileUpdateEvent(username: value));
    }

    return info;
  }

  static UserInfo? getFullUserInfo() {
    final json = SpUtil.getObject(spFullUserInfo);
    if (json != null && json is Map<String, dynamic>) {
      return UserInfo.fromJson(json);
    }
    return null;
  }

  // 获取用户手机号
  static String? getPhone() {
    final info = getUserInfo();
    return info?['phone'] as String?;
  }

  static bool isNewUser() {
    final info = getUserInfo();
    return info?['isNewUser'] as bool;
  }

  static String? getVipName() {
    final fullInfo = getFullUserInfo();
    return fullInfo?.vipName;
  }

  static bool isVip() {
    final fullInfo = getFullUserInfo();
    return fullInfo?.vip ?? false;
  }

  static MemberSingleLevel getMemberSingleLevel() {
    final fullInfo = getFullUserInfo();
    int level = fullInfo?.level ?? 0;
    switch(level) {
      case 0: return MemberSingleLevel.normalUser;
      case 1: return MemberSingleLevel.silver;
      case 2: return MemberSingleLevel.gold;
      case 3: return MemberSingleLevel.diamond;
      case 4: return MemberSingleLevel.partnerSilver;
      case 5: return MemberSingleLevel.partnerGold;
      case 6: return MemberSingleLevel.partnerDiamond;
      default: return MemberSingleLevel.normalUser;
    }
  }

  static int getMemberLevelValue() {
    final fullInfo = getFullUserInfo();
    int level = fullInfo?.level ?? 0;
    return level;
  }

  static MemberLevel getMemberLevel() {
    final fullInfo = getFullUserInfo();
    int level = fullInfo?.level ?? 0;
    if(level <= 0) {
      return MemberLevel.normal;
    } else if(level <= 3) {
      return MemberLevel.silverAgent;
    }else {
      return MemberLevel.partners;
    }
  }

  static double getBrokerage() {
    final fullInfo = getFullUserInfo();
    if (fullInfo != null) {
      return double.tryParse(fullInfo.brokeragePrice) ?? 0.0;
    }
    return 0.0;
  }

  static String? getInviteCode() {
    final fullInfo = getFullUserInfo();
    return fullInfo?.inviteCode;
  }

  static int getIntegral() {
    final fullInfo = getFullUserInfo();
    return fullInfo?.integral ?? 0;
  }

  // 检查是否登录
  static bool isLogin() {
    return getToken().isNotEmpty;
  }

  // 清除用户信息（退出登录时调用）
  static Future<void> clearUserInfo() async {
    await SpUtil.remove(spToken);
    await SpUtil.remove(spUserInfo);
    await SpUtil.remove(spFullUserInfo);
  }
}