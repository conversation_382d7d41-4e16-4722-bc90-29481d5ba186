import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/themes/colors.dart';

class BackgroundScaffold extends StatelessWidget {
  final PreferredSizeWidget? appBar;
  final String title;
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final bool noSafe;
  final List<Widget>? actions;
  final Widget? bottomNavigationBar;
  final String? backgroundImage;
  final Color? backgroundColor;
  final Color? titleColor;

  const BackgroundScaffold({
    super.key,
    this.appBar,
    required this.title,
    required this.child,
    this.padding,
    this.noSafe = false,
    this.actions,
    this.backgroundImage,
    this.backgroundColor,
    this.bottomNavigationBar,
    this.titleColor
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: this.backgroundColor ?? backgroundColor,
      bottomNavigationBar: bottomNavigationBar,
      appBar:
          appBar ??
          AppBar(
            systemOverlayStyle: SystemUiOverlayStyle.dark,
            elevation: 0,
            backgroundColor: Colors.transparent,
            centerTitle: true,
            leading: IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: Image.asset(Assets.imagesIcArrowBack),
            ),
            title: Text(
              title,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: titleColor ?? primaryTextColor,
              ),
            ),
            actions: actions,
          ),
      body: Stack(
        children: [
          Image.asset(
            backgroundImage ?? Assets.imagesBgCommon,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width,
          ),
          noSafe ? child : SafeArea(child: child),
        ],
      ),
    );
  }
}
