import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';

class BottomInfoView extends StatelessWidget {
  final String title;
  final String fieldName1;
  final String fieldName2;
  final String fieldName3;
  final String value1;
  final String value2;
  final String value3;
  final VoidCallback? onButtonTapped;

  const BottomInfoView({
    super.key,
    required this.title,
    required this.fieldName1,
    required this.fieldName2,
    required this.fieldName3,
    required this.value1,
    required this.value2,
    required this.value3,
    this.onButtonTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          R.assetsImagesBgCashInfo,
          fit: BoxFit.cover,
          width: MediaQuery.of(context).size.width,
        ),
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 26),
            Text(
              title,
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              margin: EdgeInsets.symmetric(vertical: 15, horizontal: 28),
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(0xFFF5F3F2),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                children: [
                  _buildInfoRow(
                    fieldName: fieldName1,
                    value: value1,
                  ),
                  const SizedBox(height: 12),
                  // 第二行
                  _buildInfoRow(
                    fieldName: fieldName2,
                    value: value2,
                  ),
                  const SizedBox(height: 12),
                  // 第三行
                  _buildInfoRow(
                    fieldName: fieldName3,
                    value: value3,
                  ),
                ],
              ),
            ),
            buildFinishButton(context),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoRow({
    required String fieldName,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start, // 顶部对齐
      children: [
        // 左侧字段名（限制最大宽度）
        ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 120), // 根据实际情况调整
          child: Text(
            fieldName,
            style: TextStyle(
              color: secondaryTextColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            maxLines: 2, // 允许最多2行
            overflow: TextOverflow.ellipsis, // 超出显示省略号
          ),
        ),
        const Spacer(), // 占据剩余空间
        // 右侧值（可换行）
        Expanded(
          flex: 2, // 右侧占据更多空间
          child: Text(
            value,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.right, // 右对齐
            maxLines: 3, // 允许最多3行
            overflow: TextOverflow.ellipsis, // 超出显示省略号
          ),
        ),
      ],
    );
  }

  Widget buildFinishButton(BuildContext context) {
    return TextButton(
      onPressed: () {
        Navigator.of(context).pop();
        onButtonTapped?.call();
      },
      child: Container(
        width: MediaQuery.of(context).size.width - 32,
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: fourthTextColor,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Center(
          child: Text(
            S.of(context).ok,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: primaryTextColor,
            ),
          ),
        ),
      ),
    );
  }
}

void showBottomInfoView(
  BuildContext context,
  String title,
  String fieldName1,
  String fieldName2,
  String fieldName3,
  String value1,
  String value2,
  String value3, {
  VoidCallback? onButtonTapped,
}) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return BottomInfoView(
        title: title,
        fieldName1: fieldName1,
        fieldName2: fieldName2,
        fieldName3: fieldName3,
        value1: value1,
        value2: value2,
        value3: value3,
        onButtonTapped: onButtonTapped,
      );
    },
  );
}
