import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/pages/detail/product_detail_screen.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/navigation_route.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/product_info.dart';
import '../models/share_link_product_info.dart';
import '../utils/toast_utils.dart';
import 'image_widget.dart';

class BottomProductInfoView extends StatelessWidget {
  final ProductResponse productResponse;
  final ShareLinkProductInfo productInfo;
  const BottomProductInfoView({
    super.key,
    required this.productResponse,
    required this.productInfo,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          R.assetsImagesBgCashInfo,
          fit: BoxFit.cover,
          width: MediaQuery.of(context).size.width,
        ),
        buildContent(context),
      ],
    );
  }

  Widget buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 22),
        Text(
          S.of(context).find_product,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 15,
            color: primaryTextColor,
          ),
        ),
        SizedBox(height: 16),
        ImageWidget(
          borderRadius: BorderRadius.all(Radius.circular(12)),
          width: 94,
          height: 94,
          url: productResponse.productInfo?.image ?? "",
          fit: BoxFit.cover,
        ),
        SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Text.rich(
                TextSpan(
                  children: [
                    WidgetSpan(
                      child: Image.asset(R.assetsImagesIcHomeTiktok14),
                      alignment: PlaceholderAlignment.middle,
                    ),
                    TextSpan(text: ' '),
                    TextSpan(
                      text: productResponse.productInfo?.storeName ?? "",
                      style: const TextStyle(
                        color: primaryTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                maxLines: 2,
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Text(
          productInfo.shareLink,
          style: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 13,
            color: secondaryTextColor,
          ),
        ),
        SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: InkWell(
                onTap: () async {
                  Navigator.pop(context);
                },
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 6),
                  // width: MediaQuery.of(context).size.width / 2 - 32,
                  height: 44,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFFE6AC44), Color(0xFFE5B670)],
                    ),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Center(
                    child: Text(
                      S.of(context).back,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            Expanded(
              child: InkWell(
                onTap: () async {
                  int? productId = productResponse.productInfo?.id;
                  if(productId != null) {
                    customRouter(context, ProductDetailScreen(productId: productId, productResponse: productResponse, shareLinkProductInfo: productInfo));
                  }
                },
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 6),
                  height: 44,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFFC80D1F), Color(0xFFFB4143)],
                    ),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Center(
                    child: Text(
                      S.of(context).check_cash_back,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
      ],
    );
  }
}

void showBottomProductInfoView(
  BuildContext context,
  ShareLinkProductInfo productInfo,
  ProductResponse productResponse, {
  VoidCallback? onButtonTapped,
}) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return BottomProductInfoView(
        productResponse: productResponse,
        productInfo: productInfo,
      );
    },
  );
}
