import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'blur_nav_bar_item.dart';

enum BlurEffectStyle { auto, light, dark }

class BlurNavbar extends StatefulWidget {
  const BlurNavbar({
    super.key,
    this.onTap,
    this.onDoubleTap,
    this.style = BlurEffectStyle.auto,
    this.selectedColor,
    this.borderRadius = 24,
    this.items = const [],
    this.currentIndex = 0,
    this.fontSize = 10,
    this.iconSize = 40,
  }) : assert(items.length > 1 && items.length <= 5, '至少2个，至多5个'),
       assert(currentIndex >= 0 && currentIndex < items.length);

  final BlurEffectStyle style;
  final Color? selectedColor;
  final double borderRadius;
  final int currentIndex;
  final ValueChanged<int>? onTap;
  final ValueChanged<int>? onDoubleTap;
  final List<BlurNavbarItem> items;
  final double fontSize;
  final double iconSize;

  @override
  State<BlurNavbar> createState() => _BlurNavbarState();
}

class _BlurNavbarState extends State<BlurNavbar>
    with SingleTickerProviderStateMixin {
  BlurEffectStyle get style => widget.style;

  double get borderRadius => widget.borderRadius;

  List<BlurNavbarItem> get items => widget.items;

  int get selectedIndex => widget.currentIndex;

  double get iconSize => widget.iconSize;

  double get fontSize => widget.fontSize;

  Color? get selectedColor => widget.selectedColor;

  int _previousIndex = 0;
  late AnimationController _animation;

  DateTime? _lastTapTimes;
  static const Duration _doubleTapTimeout = Duration(milliseconds: 300);

  @override
  void initState() {
    _animation = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );
    super.initState();
  }

  @override
  void dispose() {
    _animation.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const borderWidth = .7;
    final itemWidth =
        (MediaQuery.of(context).size.width - borderWidth * 2) / items.length;

    final sColor = selectedColor ?? Color(0xFF3C3A36);
    double bottomPadding = MediaQuery.of(context).padding.bottom;
    Logger().d("bottomPadding: $bottomPadding");
    double baseHeight = 65;
    if (Platform.isIOS) {
      baseHeight = 49;
      bottomPadding = bottomPadding == 0 ? 0 : bottomPadding - 4;
    }
    double barHeight = baseHeight + bottomPadding;

    final radius = Radius.circular(borderRadius);

    return Container(
      alignment: Alignment.bottomCenter,
      height: barHeight,
      child: AnimatedContainer(
        duration: Duration.zero,
        height: barHeight,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(topLeft: radius, topRight: radius),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.only(topLeft: radius, topRight: radius),
          child: Stack(
            children: [
              Backdrop(
                controller: _animation,
                color: sColor,
                width: itemWidth,
                height: barHeight,
                previousIndex: _previousIndex,
                selectedIndex: selectedIndex,
              ),
              /*backdrop*/
              BackdropFilter(
                blendMode: BlendMode.src,
                filter: ImageFilter.blur(sigmaX: 26, sigmaY: 26),
                child: Container(
                  width: double.infinity,
                  color: (() {
                    final lightColor = Colors.white.withValues(alpha: 1);
                    return lightColor;
                  })(),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: items
                        .asMap()
                        .map(
                          (idx, item) => MapEntry(
                            idx,
                            _itemBuilder(context, idx, item, itemWidth),
                          ),
                        )
                        .values
                        .toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onTap(int index) {
    final now = DateTime.now();

    //只有首页需要点击两次才能返回顶部
    if (selectedIndex == index && !_animation.isAnimating) {
      if (_lastTapTimes != null &&
          now.difference(_lastTapTimes!) <= _doubleTapTimeout) {
        Logger().d("Double tap detected on tab $index");
        widget.onDoubleTap?.call(index);
        _lastTapTimes = null;
      } else {
        _lastTapTimes = now;
      }
      return;
    }
    Logger().d("selectedIndex: $selectedIndex, index: $index");
    widget.onTap!(index);
    _animation.forward(from: 0.0);
    _previousIndex = selectedIndex;
  }

  Widget _itemBuilder(
    BuildContext context,
    int index,
    BlurNavbarItem item,
    double width,
  ) {
    return GestureDetector(
      onTap: () => _onTap(index),
      child: Container(
        width: width,
        alignment: Alignment.center,
        decoration: const BoxDecoration(color: Colors.transparent),
        child: Column(
          children: [
            SizedBox(height: Platform.isAndroid ? 8 : 4),
            Container(
              width: iconSize,
              height: iconSize,
              alignment: Alignment.bottomCenter,
              child: selectedIndex == index ? item.selectIcon : item.icon,
            ),
            Text(
              item.title ?? "",
              textAlign: TextAlign.center,
              maxLines: 1,
              style: TextStyle(
                fontSize: fontSize,
                color: (() {
                  var lightColor = selectedIndex == index
                      ? selectedColor
                      : Color(0xFF666666);
                  return lightColor;
                })(),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

class Cursor extends StatefulWidget {
  const Cursor({
    super.key,
    required this.color,
    required this.width,
    required this.selectedIndex,
    required this.previousIndex,
    required this.controller,
  });

  final Color color;
  final double width;
  final int selectedIndex;
  final int previousIndex;
  final AnimationController controller;

  @override
  State<Cursor> createState() => _CursorState();
}

class _CursorState extends State<Cursor> {
  double get _translateX =>
      Tween<double>(
            begin: widget.previousIndex * widget.width,
            end: widget.selectedIndex * widget.width,
          )
          .animate(
            CurvedAnimation(
              parent: widget.controller,
              curve: Curves.easeInOutBack,
            ),
          )
          .value;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: 5,
      alignment: Alignment.center,
      child: AnimatedBuilder(
        animation: widget.controller,
        builder: (ctx, child) => Container(
          width: 28,
          height: 5,
          transform: Matrix4.translationValues(_translateX, 0, 0),
          decoration: BoxDecoration(
            color: widget.color,
            borderRadius: const BorderRadius.all(Radius.circular(3)),
          ),
        ),
      ),
    );
  }
}

class Backdrop extends StatefulWidget {
  const Backdrop({
    super.key,
    required this.color,
    required this.width,
    required this.height,
    required this.selectedIndex,
    required this.previousIndex,
    required this.controller,
  });

  final Color color;
  final double width;
  final double height;
  final int selectedIndex;
  final int previousIndex;
  final AnimationController controller;

  @override
  State<Backdrop> createState() => _BackdropState();
}

class _BackdropState extends State<Backdrop> {
  double get _translateX =>
      Tween<double>(
            begin: widget.previousIndex * widget.width,
            end: widget.selectedIndex * widget.width,
          )
          .animate(
            CurvedAnimation(
              parent: widget.controller,
              curve: Curves.easeInOutBack,
            ),
          )
          .value;

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (ctx, child) => Container(
        width: widget.width,
        height: widget.height,
        transformAlignment: Alignment.center,
        transform: Matrix4.translationValues(_translateX, 0, 0),
        decoration: BoxDecoration(color: widget.color, shape: BoxShape.circle),
      ),
    );
  }
}
