import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/pages/member/member_level.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';

class BottomTableInfoView extends StatelessWidget {
  final String title;
  final TableModel tableModel;

  const BottomTableInfoView({
    super.key,
    required this.title,
    required this.tableModel,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          R.assetsImagesBgCashInfo,
          fit: BoxFit.cover,
          width: MediaQuery.of(context).size.width,
        ),
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 26),
            Text(
              title,
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 15, left: 28, right: 28, bottom: 15 + MediaQuery.of(context).padding.bottom),
              padding: EdgeInsets.all(2),
              decoration: BoxDecoration(
                color:Color(0xFFFFF9F4),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Color(0xFFFFF9F4), width: 1)
              ),
              child: _buildFixedHeightTable(context)
            ),
          ],
        ),
      ],
    );
  }

 
  Widget _buildFixedHeightTable(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        minHeight: (tableModel.rows.length + 1)*48,
        maxHeight: (tableModel.rows.length + 1)*48,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 表头
          _buildTableHeader(),
          // 表格内容 - 使用自定义分隔线
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: ClampingScrollPhysics(),
              itemCount: tableModel.rows.length,
              itemBuilder: (context, index) =>
                  _buildTableRowWithDivider(index, tableModel.rows[index], index == tableModel.rows.length - 1),
            ),
          ),
        ],
      ),
    );
  }

  // 表头构建方法保持不变
  Widget _buildTableHeader() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      decoration: BoxDecoration(
        color: Color(0xFFFEF4E0),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: List.generate(tableModel.headers.length, (colIndex) {
          return Expanded(
            flex: colIndex == 0 ? 1 : 2,
            child: Text(
              tableModel.headers[colIndex],
              textAlign: TextAlign.center,
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          );
        }),
      ),
    );
  }

// 带分割线的表格行
  Widget _buildTableRowWithDivider(int index, List<dynamic> rowData, bool isLastRow) {
    return Column(
      children: [
        Container(
          height: 48, // 固定行高
          padding: EdgeInsets.symmetric(horizontal: 12),
          child: Row(
            children: List.generate(rowData.length, (colIndex) {
              return Expanded(
                flex: colIndex == 0 ? 1 : 2,
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    '${rowData[colIndex]}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: colIndex == 0 ? primaryTextColor : secondaryTextColor,
                      fontSize: 12,
                      fontWeight: colIndex == 0 ? FontWeight.w700 : FontWeight.w400
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
        // 最后一行不加分隔线
        if (!isLastRow) Divider(
          height: 1,
          thickness: 1,
          color: Color(0xFFFDEBD3), // 使用更浅的分隔线颜色
          indent: 0, // 左侧缩进
          endIndent: 0, // 右侧缩进
        ),
      ],
    );
  }
}

void showBottomTableInfoView(
  BuildContext context,
  String title,
  TableModel tableModel,
) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return BottomTableInfoView(title: title, tableModel: tableModel);
    },
  );
}
