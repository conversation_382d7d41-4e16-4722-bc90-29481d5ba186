import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/themes/colors.dart';

typedef CustomDialogCallback = void Function(BuildContext context);

void showCustomDialog({
  required BuildContext context,
  required String title,
  required String description,
  String confirmButtonText = 'OK',
  String cancelButtonText = 'Cancel',
  double borderRadius = 16.0,
  Duration animationDuration = const Duration(milliseconds: 300),
  Curve animationCurve = Curves.easeOutBack,
  CustomDialogCallback? onConfirm,
  CustomDialogCallback? onCancel,
}) {
  showGeneralDialog(
    context: context,
    barrierDismissible: false,
    barrierColor: Colors.black.withValues(alpha: 0.4),
    transitionDuration: animationDuration,
    pageBuilder:
        (
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
        ) {
          return Material(
            color: Colors.transparent,
            child: Center(
              child: ScaleTransition(
                scale: CurvedAnimation(
                  parent: animation,
                  curve: Interval(0.0, 1.0, curve: animationCurve),
                ),
                child: FadeTransition(
                  opacity: CurvedAnimation(
                    parent: animation,
                    curve: Interval(0.0, 1.0, curve: Curves.easeOut),
                  ),
                  child: _buildDialogContent(
                    context,
                    title,
                    description,
                    confirmButtonText,
                    cancelButtonText,
                    borderRadius,
                    onConfirm,
                    onCancel,
                  ),
                ),
              ),
            ),
          );
        },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return ScaleTransition(
        scale: CurvedAnimation(
          parent: animation,
          curve: animationCurve,
        ).drive(Tween(begin: 0.8, end: 1.0)),
        child: FadeTransition(
          opacity: CurvedAnimation(parent: animation, curve: Curves.easeOut),
          child: child,
        ),
      );
    },
  );
}

Widget _buildDialogContent(
  BuildContext context,
  String title,
  String description,
  String confirmButtonText,
  String cancelButtonText,
  double borderRadius,
  CustomDialogCallback? onConfirm,
  CustomDialogCallback? onCancel,
) {
  return Dialog(
    backgroundColor: Colors.transparent,
    elevation: 0,
    child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 16,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Stack(
        children: [
          // 背景图片
          ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius),
            child: Image.asset(
              Assets.imagesBgIncomeDialog,
              fit: BoxFit.cover,
              width: MediaQuery.of(context).size.width,
            ),
          ),

          // 内容区域
          Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                // 描述
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: unSelectedTextColor,
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),

                buildButtonGroup(
                  context,
                  onConfirm,
                  confirmButtonText,
                  cancelButtonText,
                  onCancel: onCancel,
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget buildButtonGroup(
  BuildContext dialogContext,
  CustomDialogCallback? onConfirm,
  String confirmButtonText,
  String cancelButtonText, {
  CustomDialogCallback? onCancel,
}) {
  if (onCancel == null) {
    return GestureDetector(
      onTap: () {
        if (onConfirm != null) {
          onConfirm(dialogContext); // Pass the dialog context to the callback
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        height: 40,
        decoration: BoxDecoration(
          color: fourthTextColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        alignment: Alignment.center,
        child: Text(
          confirmButtonText,
          style: TextStyle(
            fontSize: 13,
            color: primaryTextColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  } else {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              Navigator.of(dialogContext).pop();
              onCancel(
                dialogContext,
              ); // Pass the dialog context to the callback
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 5),
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              alignment: Alignment.center,
              child: Text(
                cancelButtonText,
                style: TextStyle(
                  fontSize: 13,
                  color: primaryTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),

        Expanded(
          child: GestureDetector(
            onTap: () {
              Navigator.of(dialogContext).pop();
              if (onConfirm != null) {
                onConfirm(dialogContext);
              }
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 5),
              height: 40,
              decoration: BoxDecoration(
                color: fourthTextColor,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              alignment: Alignment.center,
              child: Text(
                confirmButtonText,
                style: TextStyle(
                  fontSize: 11,
                  color: primaryTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
