import 'package:flutter/material.dart';

class DashedDivider extends StatelessWidget {
  final double height;
  final double dashLength;
  final double dashGap;
  final Color color;

  const DashedDivider({
    this.height = 1.0,
    this.dashLength = 4.0,
    this.dashGap = 2.0,
    this.color = Colors.grey,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final dashCount = (constraints.maxWidth / (dashLength + dashGap)).floor();
        return Row(
          children: List.generate(dashCount, (index) {
            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: index == dashCount - 1 ? 0 : dashGap),
                child: Container(
                  height: height,
                  color: color,
                ),
              ),
            );
          }),
        );
      },
    );
  }
}