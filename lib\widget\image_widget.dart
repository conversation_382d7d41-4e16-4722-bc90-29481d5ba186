import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:logger/logger.dart';
import 'package:milestone/r.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:milestone/widget/skeleton_widget.dart';

class ImageWidget extends StatefulWidget {
  final String url;
  final double? width;
  final double? height;
  final String defaultImagePath;
  final BoxFit fit;
  final double? loadingWidth;
  final double? loadingHeight;
  final BorderRadius borderRadius;
  final bool isClearCache;
  final Alignment alignment;

  const ImageWidget(
      {super.key,
      required this.url,
      this.width,
      this.height,
      this.defaultImagePath = "",
      this.fit = BoxFit.cover,
      this.loadingWidth,
      this.loadingHeight,
      this.borderRadius = BorderRadius.zero,
      this.isClearCache = false,
      this.alignment = Alignment.center});

  @override
  State<StatefulWidget> createState() => StateImageWidget();
}

class StateImageWidget extends State<ImageWidget> {
  bool isLoadingSuccess = false;
  late Widget imageWidget;

  late String url;

  @override
  void initState() {
    super.initState();
    url = widget.url;
    if (widget.isClearCache == true) {
      clearCache(url);
    }
    Logger().d(widget.url);
    imageUpdate(url);
  }

  @override
  void didUpdateWidget(ImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.url != widget.url) {
      imageUpdate(widget.url);
    }
  }

  Future<void> clearCache(String imageUrl) async {
    await CachedNetworkImage.evictFromCache(imageUrl);
  }

  void imageUpdate(String url) {
    try {
      if (isUrl(url)) {
        imageWidget = CachedNetworkImage(
          imageUrl: url,
          width: widget.width,
          height: widget.height,
          fit: widget.fit,
          alignment: widget.alignment,
          fadeInDuration: const Duration(milliseconds: 1),
          fadeOutDuration: const Duration(milliseconds: 1),
          placeholder: (context, url) => SkeletonWidget(
            width: widget.loadingWidth,
            height: widget.loadingHeight,
          ),
          errorWidget: (context, url, error) => Image.asset(
            widget.defaultImagePath.isEmpty
                ? R.assetsImagesPlaceholder
                : widget.defaultImagePath,
            width: widget.width,
            height: widget.height,
            fit: BoxFit.cover,
          ),
        );
      } else if (isBase64(url)) {
        Logger().d("imageUpdate");
        updateBase64Content(url);
      } else {
        Logger().d("imageUpdate");
        imageWidget = Image.asset(
          widget.defaultImagePath.isEmpty
              ? R.assetsImagesPlaceholder
              : widget.defaultImagePath,
          width: widget.width,
          height: widget.height,
          fit: BoxFit.cover,
        );
      }
    } on Exception {
      Logger().d("image update");
    }
  }

  bool isBase64(String input) {
    final RegExp base64Regex = RegExp(r'^[A-Za-z0-9+/]*={0,2}$');
    if (!base64Regex.hasMatch(input)) {
      return false; // 不匹配Base64编码的模式，返回false
    }

    // 检查字符串长度是否符合Base64编码的要求
    if (input.length % 4 != 0) {
      return false; // 长度不是4的倍数，返回false
    }

    if (input.isEmpty) {
      return false;
    }

    return true; // 符合Base64编码的模式和长度要求，返回true
  }

  void update(String url) {
    setState(() {
      imageUpdate(url);
    });
  }

  void updateFile(File file) {
    setState(() {
      updateFileContent(file);
    });
  }

  void updateBase64(String base64) {
    setState(() {
      updateBase64Content(base64);
    });
  }

  void updateFileContent(File file) {
    try {
      imageWidget = Image.file(file,
          width: widget.width, height: widget.height, fit: BoxFit.cover);
    } on Exception {
      Logger().d("file update");
    }
  }

  void updateBase64Content(String base64Value) {
    try {
      Uint8List bytes = base64.decode(base64Value);
      imageWidget = Image.memory(bytes,
          width: widget.width, height: widget.height, fit: BoxFit.cover);
    } on Exception {
      Logger().d("file update");
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: ClipRRect(
        borderRadius: widget.borderRadius,
        child: imageWidget,
      ),
    );
  }
}
