import 'package:flutter/material.dart';

import '../generated/assets.dart';
import '../models/user_level.dart';
import '../themes/colors.dart';

class MemberLevelBadgeView extends StatelessWidget {
  final int memberLevel;
  const MemberLevelBadgeView({super.key, required this.memberLevel});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(memberLevelBackgroundImage(memberLevel)),
        Positioned(
          bottom: 4,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(width: 18),
              Text(
                memberLevelName(memberLevel),
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: primaryTextColor,
                ),
              ),
              Image.asset(
                Assets.imagesIcMeArrowLeft,
                width: 11,
                height: 11,
              ),
            ],
          ),
        ),
      ],
    );
  }

}