import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/themes/colors.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../generated/l10n.dart';
import 'common_dialog.dart';

void showQRcodeDialog({
  required BuildContext context,
  required String urlLink,
  double borderRadius = 16.0,
  Duration animationDuration = const Duration(milliseconds: 300),
  Curve animationCurve = Curves.easeOutBack,
  CustomDialogCallback? onConfirm,
  CustomDialogCallback? onCancel,
}) {
  showGeneralDialog(
    context: context,
    barrierDismissible: false,
    barrierColor: Colors.black.withValues(alpha: 0.4),
    transitionDuration: animationDuration,
    pageBuilder:
        (
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
        ) {
          return Material(
            color: Colors.transparent,
            child: Center(
              child: ScaleTransition(
                scale: CurvedAnimation(
                  parent: animation,
                  curve: Interval(0.0, 1.0, curve: animationCurve),
                ),
                child: FadeTransition(
                  opacity: CurvedAnimation(
                    parent: animation,
                    curve: Interval(0.0, 1.0, curve: Curves.easeOut),
                  ),
                  child: _buildDialogContent(context, urlLink, onConfirm, onCancel),
                ),
              ),
            ),
          );
        },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return ScaleTransition(
        scale: CurvedAnimation(
          parent: animation,
          curve: animationCurve,
        ).drive(Tween(begin: 0.8, end: 1.0)),
        child: FadeTransition(
          opacity: CurvedAnimation(parent: animation, curve: Curves.easeOut),
          child: child,
        ),
      );
    },
  );
}

Widget _buildDialogContent(
  BuildContext dialogContext,
  String urlLink,
  CustomDialogCallback? onConfirm,
  CustomDialogCallback? onCancel,
) {
  double borderRadius = 16.0;
  return Dialog(
    backgroundColor: Colors.transparent,
    elevation: 0,
    child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 16,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Stack(
        children: [
          // 背景图片
          ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius),
            child: Image.asset(
              Assets.imagesBgIncomeDialog,
              fit: BoxFit.cover,
              width: MediaQuery.of(dialogContext).size.width,
            ),
          ),

          // 内容区域
          Container(
            margin: EdgeInsets.all(12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  S.of(dialogContext).pay_with_qrcode,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                Text(
                  S.of(dialogContext).pay_with_qrcode_usage,
                  style: TextStyle(
                    fontSize: 12,
                    color: unSelectedTextColor,
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                QrImageView(data: urlLink, version: QrVersions.auto, size: 200.0),
                SizedBox(height: 12),
                buildButtonGroup(dialogContext, onConfirm, onCancel),
              ],
            ),
          )
        ],
      ),
    ),
  );
}

Widget buildButtonGroup(
  BuildContext dialogContext,
  CustomDialogCallback? onConfirm,
  CustomDialogCallback? onCancel,
) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceAround,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Expanded(
        flex: 1,
        child: InkWell(
          onTap: () {
            Navigator.of(dialogContext).pop();
            if (onCancel != null) {
              onCancel(
                dialogContext,
              ); // Pass the dialog context to the callback
            }
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            alignment: Alignment.center,
            child: Text(
              S.of(dialogContext).finish,
              style: TextStyle(
                fontSize: 13,
                color: primaryTextColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),

      Expanded(
        flex: 2,
        child: InkWell(
          onTap: () {
            if (onConfirm != null) {
              onConfirm(
                dialogContext,
              ); // Pass the dialog context to the callback
            }
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            height: 40,
            decoration: BoxDecoration(
              color: fourthTextColor,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            alignment: Alignment.center,
            child: Center(
              child: Text(
                S.of(dialogContext).open_payment_link,
                style: TextStyle(
                  fontSize: 13,
                  color: primaryTextColor,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ),
    ],
  );
}
