import 'package:flutter/material.dart';

class SkeletonWidget extends StatefulWidget {
  final double? width;
  final double? height;
  final Duration animationDuration;

  const SkeletonWidget(
      {super.key,
      this.width = 20,
      this.height = 200,
      this.animationDuration = const Duration(milliseconds: 1500)});

  @override
  State<StatefulWidget> createState() => SkeletonWidgetState();
}

class SkeletonWidgetState extends State<SkeletonWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController animationController;
  late Animation gradientAnimation;

  @override
  void initState() {
    super.initState();
    animationController =
        AnimationController(duration: widget.animationDuration, vsync: this);
    gradientAnimation = Tween<double>(begin: -3, end: 10).animate(
        CurvedAnimation(parent: animationController, curve: Curves.linear))
      ..addListener(_animationListener);
    animationController.addListener(_animationListener);
    animationController.repeat();
  }

  void _animationListener() {
    setState(() {});
  }

  @override
  void dispose() {
    animationController.removeListener(_animationListener);
    animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
          gradient: LinearGradient(
              begin: Alignment(gradientAnimation.value, 0),
              end: const Alignment(-1, 0),
              colors: [
            Colors.grey.shade50,
            Colors.white54,
            Colors.grey.shade50
          ])),
    );
  }
}
