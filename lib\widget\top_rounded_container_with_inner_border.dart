import 'package:flutter/material.dart';

class TopRoundedContainerWithInnerBorder extends StatelessWidget {
  final Widget child;
  final double cornerRadius;
  final double borderWidth;
  final Color borderColor;
  final Color backgroundColor;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;

  const TopRoundedContainerWithInnerBorder({
    super.key,
    required this.child,
    this.cornerRadius = 16,
    this.borderWidth = 1,
    this.borderColor = Colors.white,
    this.backgroundColor = Colors.transparent,
    this.padding,
    this.margin,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      child: ClipRect(
        child: CustomPaint(
          painter: _TopRoundedInnerBorderPainter(
            cornerRadius: cornerRadius,
            borderWidth: borderWidth,
            borderColor: borderColor,
            backgroundColor: backgroundColor,
          ),
          child: Padding(
            padding: padding ?? EdgeInsets.zero,
            child: child,
          ),
        ),
      ),
    );
  }
}

class _TopRoundedInnerBorderPainter extends CustomPainter {
  final double cornerRadius;
  final double borderWidth;
  final Color borderColor;
  final Color backgroundColor;

  _TopRoundedInnerBorderPainter({
    required this.cornerRadius,
    required this.borderWidth,
    required this.borderColor,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = backgroundColor;

    final borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = borderColor
      ..strokeWidth = borderWidth;

    // 创建顶部带圆角的路径（上方两个圆角，下方两个直角）
    final path = Path()
      ..moveTo(0, cornerRadius)
      ..arcToPoint(
        Offset(cornerRadius, 0),
        radius: Radius.circular(cornerRadius),
      )
      ..lineTo(size.width - cornerRadius, 0)
      ..arcToPoint(
        Offset(size.width, cornerRadius),
        radius: Radius.circular(cornerRadius),
      )
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height)
      ..close();

    // 绘制背景
    canvas.drawPath(path, paint);

    // 绘制内描边
    final innerPath = Path()
      ..moveTo(borderWidth, cornerRadius)
      ..arcToPoint(
        Offset(cornerRadius, borderWidth),
        radius: Radius.circular(cornerRadius),
      )
      ..lineTo(size.width - cornerRadius, borderWidth)
      ..arcToPoint(
        Offset(size.width - borderWidth, cornerRadius),
        radius: Radius.circular(cornerRadius),
      )
      ..lineTo(size.width - borderWidth, size.height - borderWidth)
      ..lineTo(borderWidth, size.height - borderWidth)
      ..close();

    canvas.drawPath(innerPath, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}