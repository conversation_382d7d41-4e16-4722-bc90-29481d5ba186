import 'package:flutter/material.dart';

class VerticalDashedDivider extends StatelessWidget {
  final double height;
  final double dashHeight;
  final double dashSpacing;
  final Color color;

  const VerticalDashedDivider({
    super.key,
    this.height = double.infinity,
    this.dashHeight = 4,
    this.dashSpacing = 4,
    this.color = Colors.grey,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: 1,
      child: CustomPaint(
        painter: _DashedLinePainter(
          dashHeight: dashHeight,
          dashSpacing: dashSpacing,
          color: color,
        ),
      ),
    );
  }
}

class _DashedLinePainter extends CustomPainter {
  final double dashHeight;
  final double dashSpacing;
  final Color color;

  _DashedLinePainter({
    required this.dashHeight,
    required this.dashSpacing,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = size.width;

    double startY = 0;
    while (startY < size.height) {
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpacing;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
